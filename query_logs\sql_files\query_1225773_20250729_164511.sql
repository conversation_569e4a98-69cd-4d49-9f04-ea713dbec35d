﻿-- Query ID: 1225773
-- Start Time: 2025-07-29 11:01:24.952644
-- End Time: 2025-07-29 11:01:26.884146
-- Database: dev
-- Query Type: SELECT
-- Status: success
-- Duration: 1932 ms
-- User: IAM:dev_azure_devops_user
-- Result: 
-- Logged: 07/29/2025 16:45:11
-- ================================================

WITH complete_query AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        h.elapsed_time,\n        h.queue_time,\n        h.execution_time,\n        h.compile_time,\n        h.planning_time,\n        h.lock_wait_time,\n        h.usage_limit,\n        h.compute_type,\n        h.redshift_version,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.query_id = 1073946732\n)\nSELECT \n    query_id,\n    start_time,\n    end_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    elapsed_time,\n    queue_time,\n    execution_time,\n    compile_time,\n    planning_time,\n    lock_wait_time,\n    returned_rows,\n    returned_bytes,\n    username,\n    usage_limit,\n    compute_type,\n    redshift_version,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_query;
