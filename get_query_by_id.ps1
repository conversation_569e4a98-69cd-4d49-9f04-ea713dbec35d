#!/usr/bin/env powershell

# Script to get complete query text by Query ID from Redshift Serverless
# Usage: .\get_query_by_id.ps1 -QueryId 1224653
# Example: .\get_query_by_id.ps1 -QueryId 1225488

param(
    [Parameter(Mandatory=$true)]
    [long]$QueryId,
    [string]$WorkgroupName = "redshift-poc",
    [string]$Database = "dev",
    [string]$Region = "us-east-1"
)

Write-Host "Getting complete query details for Query ID: $QueryId" -ForegroundColor Green

# SQL query to get complete query details with full query text
$sql = @"
WITH complete_query AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        h.elapsed_time,
        h.queue_time,
        h.execution_time,
        h.compile_time,
        h.planning_time,
        h.lock_wait_time,
        h.usage_limit,
        h.compute_type,
        h.redshift_version,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.query_id = $QueryId
)
SELECT 
    query_id,
    start_time,
    end_time,
    database_name,
    query_type,
    status,
    duration_ms,
    elapsed_time,
    queue_time,
    execution_time,
    compile_time,
    planning_time,
    lock_wait_time,
    returned_rows,
    returned_bytes,
    username,
    usage_limit,
    compute_type,
    redshift_version,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_query;
"@

try {
    Write-Host "Executing query on Redshift Serverless..." -ForegroundColor Yellow
    
    # Execute the query
    $result = aws redshift-data execute-statement `
        --workgroup-name $WorkgroupName `
        --database $Database `
        --sql $sql `
        --region $Region | ConvertFrom-Json
    
    $statementId = $result.Id
    Write-Host "Query submitted with ID: $statementId" -ForegroundColor Cyan
    
    # Wait for query to complete
    do {
        Start-Sleep -Seconds 2
        $status = aws redshift-data describe-statement --id $statementId --region $Region | ConvertFrom-Json
        Write-Host "Query status: $($status.Status)" -ForegroundColor Yellow
    } while ($status.Status -eq "RUNNING" -or $status.Status -eq "SUBMITTED" -or $status.Status -eq "PICKED" -or $status.Status -eq "STARTED")
    
    if ($status.Status -eq "FINISHED") {
        Write-Host "Query completed successfully!" -ForegroundColor Green
        
        if ($status.ResultRows -eq 0) {
            Write-Host "No query found with ID: $QueryId" -ForegroundColor Red
            Write-Host "The query might be too old (outside retention period) or the ID might be incorrect." -ForegroundColor Yellow
            return
        }
        
        Write-Host "Fetching results..." -ForegroundColor Yellow
        
        # Get the results
        $queryResults = aws redshift-data get-statement-result --id $statementId --region $Region | ConvertFrom-Json
        
        Write-Host "`n" + "=" * 80 -ForegroundColor Magenta
        Write-Host "COMPLETE QUERY DETAILS FOR ID: $QueryId" -ForegroundColor Magenta
        Write-Host "=" * 80 -ForegroundColor Magenta
        
        # Display results in a formatted way
        $record = $queryResults.Records[0]
        
        $queryIdResult = $record[0].longValue
        $startTime = $record[1].stringValue
        $endTime = if ($record[2].stringValue) { $record[2].stringValue } else { "N/A" }
        $database = $record[3].stringValue
        $queryType = $record[4].stringValue
        $status = $record[5].stringValue.Trim()
        $durationMs = if ($record[6].longValue) { $record[6].longValue } else { "N/A" }
        $elapsedTime = if ($record[7].longValue) { $record[7].longValue } else { "N/A" }
        $queueTime = if ($record[8].longValue) { $record[8].longValue } else { "N/A" }
        $executionTime = if ($record[9].longValue) { $record[9].longValue } else { "N/A" }
        $compileTime = if ($record[10].longValue) { $record[10].longValue } else { "N/A" }
        $planningTime = if ($record[11].longValue) { $record[11].longValue } else { "N/A" }
        $lockWaitTime = if ($record[12].longValue) { $record[12].longValue } else { "N/A" }
        $returnedRows = if ($record[13].longValue) { $record[13].longValue } else { 0 }
        $returnedBytes = if ($record[14].longValue) { $record[14].longValue } else { 0 }
        $username = $record[15].stringValue.Trim()
        $usageLimit = if ($record[16].stringValue) { $record[16].stringValue } else { "N/A" }
        $computeType = if ($record[17].stringValue) { $record[17].stringValue } else { "N/A" }
        $redshiftVersion = if ($record[18].stringValue) { $record[18].stringValue } else { "N/A" }
        $result = $record[19].stringValue
        $fullQuery = $record[20].stringValue
        
        Write-Host "`nQUERY INFORMATION:" -ForegroundColor Cyan
        Write-Host "Query ID: $queryIdResult" -ForegroundColor White
        Write-Host "Start Time: $startTime" -ForegroundColor Gray
        Write-Host "End Time: $endTime" -ForegroundColor Gray
        Write-Host "Database: $database" -ForegroundColor Gray
        Write-Host "Query Type: $queryType" -ForegroundColor Gray
        Write-Host "Status: $status" -ForegroundColor $(if ($status -eq "success") { "Green" } else { "Red" })
        Write-Host "User: $username" -ForegroundColor Gray
        
        Write-Host "`nPERFORMANCE METRICS:" -ForegroundColor Cyan
        Write-Host "Duration: $durationMs ms" -ForegroundColor Gray
        Write-Host "Elapsed Time: $elapsedTime ms" -ForegroundColor Gray
        Write-Host "Queue Time: $queueTime ms" -ForegroundColor Gray
        Write-Host "Execution Time: $executionTime ms" -ForegroundColor Gray
        Write-Host "Compile Time: $compileTime ms" -ForegroundColor Gray
        Write-Host "Planning Time: $planningTime ms" -ForegroundColor Gray
        Write-Host "Lock Wait Time: $lockWaitTime ms" -ForegroundColor Gray
        
        Write-Host "`nRESULT INFORMATION:" -ForegroundColor Cyan
        Write-Host "Returned Rows: $returnedRows" -ForegroundColor Gray
        Write-Host "Returned Bytes: $returnedBytes" -ForegroundColor Gray
        Write-Host "Result: $result" -ForegroundColor $(if ($status -eq "success") { "Green" } else { "Red" })
        Write-Host "Usage Limit: $usageLimit" -ForegroundColor Gray
        Write-Host "Compute Type: $computeType" -ForegroundColor Gray
        Write-Host "Redshift Version: $redshiftVersion" -ForegroundColor Gray
        
        Write-Host "`nCOMPLETE QUERY TEXT:" -ForegroundColor Yellow
        Write-Host "=" * 80 -ForegroundColor Yellow
        Write-Host $fullQuery -ForegroundColor White
        Write-Host "=" * 80 -ForegroundColor Yellow
        
        # Save to file
        $outputFile = "query_${QueryId}_details_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        $queryResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputFile -Encoding UTF8
        Write-Host "`nComplete details saved to: $outputFile" -ForegroundColor Green
        
        # Also save just the query text to a SQL file
        $sqlFile = "query_${QueryId}_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
        $fullQuery | Out-File -FilePath $sqlFile -Encoding UTF8
        Write-Host "Query text saved to: $sqlFile" -ForegroundColor Green
        
    } else {
        Write-Host "Query failed with status: $($status.Status)" -ForegroundColor Red
        if ($status.Error) {
            Write-Host "Error: $($status.Error)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nScript completed!" -ForegroundColor Green
