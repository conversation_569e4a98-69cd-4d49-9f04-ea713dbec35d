# QA Environment Configuration - PUBLIC ACCESS
# Basic Configuration
db_name              = "presence-postgres-qa"
key_vault_name       = "presence-kv-qa"
db_username_secret   = "postgres-admin-username"
db_password_secret   = "postgres-admin-password"
location             = "East US"
rg_name              = "presence-rg-qa"

# Server Configuration - Higher specs than DEV
db_version    = "13"
sku_name      = "GP_Standard_D2s_v3"  # General Purpose for QA
storage_mb    = 65536                 # 64 GB
storage_tier  = "P6"
zone          = 1

# Database Configuration
databases = ["presence_qa", "postgres"]

# Firewall Rules for Public Access (Update with your actual IP ranges)
allowed_ip_ranges = {
  "office_network" = {
    start_ip = "***********"    # Replace with your office IP range
    end_ip   = "*************"
  }
  "qa_testing_team" = {
    start_ip = "************"   # Replace with QA team IP range
    end_ip   = "**************"
  }
  "automation_tools" = {
    start_ip = "*********"      # Replace with automation tools IP range
    end_ip   = "***********"
  }
  "ci_cd_pipeline" = {
    start_ip = "***********"    # Replace with CI/CD pipeline IP
    end_ip   = "***********"
  }
}

# PostgreSQL Configurations for QA
postgresql_configurations = {
  "log_statement"              = "mod"
  "log_min_duration_statement" = "2000"
  "log_connections"            = "on"
  "log_disconnections"         = "on"
  "connection_throttling"      = "on"
  "shared_preload_libraries"   = "pg_stat_statements"
}

# Tags
tags = {
  Environment = "qa"
  Project     = "presence-cloud"
  ManagedBy   = "terraform"
  AccessType  = "public"
}
