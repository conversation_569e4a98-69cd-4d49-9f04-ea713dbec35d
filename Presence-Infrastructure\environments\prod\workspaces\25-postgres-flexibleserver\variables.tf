# Basic Configuration Variables
variable "db_name" {
  type        = string
  description = "Name of the PostgreSQL Flexible Database Server"
}

variable "key_vault_name" {
  type        = string
  description = "Key Vault Name"
}

variable "db_username_secret" {
  type        = string
  description = "Database username secret name in Key Vault"
}

variable "db_password_secret" {
  type        = string
  description = "Database password secret name in Key Vault"
}

variable "location" {
  type        = string
  description = "Azure region where resources will be deployed"
}

variable "rg_name" {
  type        = string
  description = "Resource Group Name"
}

variable "db_version" {
  type        = string
  description = "PostgreSQL version"
  default     = "13"
}

# Network Configuration Variables (PRIVATE ACCESS for PRODUCTION)
variable "subnet_id" {
  type        = string
  description = "Subnet ID for private access (required for production)"
}

variable "vnet_id" {
  type        = string
  description = "Virtual Network ID for private DNS zone linking"
}

variable "private_dns_zone_name" {
  type        = string
  description = "Name of the private DNS zone for PostgreSQL"
}

variable "dns_zone_link_name" {
  type        = string
  description = "Name of the private DNS zone virtual network link"
}

# Server Configuration Variables (PRODUCTION SPECS)
variable "sku_name" {
  type        = string
  description = "SKU name for the PostgreSQL server"
  default     = "GP_Standard_D4s_v3"  # General Purpose 4 vCores for Production
}

variable "storage_mb" {
  type        = number
  description = "Storage size in MB"
  default     = 131072  # 128 GB for Production
}

variable "storage_tier" {
  type        = string
  description = "Storage tier"
  default     = "P10"
}

variable "zone" {
  type        = number
  description = "Availability zone for the server"
  default     = 1
}

# High Availability Configuration (PRODUCTION)
variable "high_availability_enabled" {
  type        = bool
  description = "Enable high availability for the PostgreSQL server"
  default     = true
}

variable "standby_availability_zone" {
  type        = number
  description = "Availability zone for the standby server (for HA)"
  default     = 2
}

# Backup Configuration (PRODUCTION)
variable "backup_retention_days" {
  type        = number
  description = "Backup retention period in days"
  default     = 35  # 5 weeks for production
}

variable "geo_redundant_backup_enabled" {
  type        = bool
  description = "Enable geo-redundant backup"
  default     = true
}

# Maintenance Window Configuration (PRODUCTION)
variable "maintenance_window" {
  type = object({
    day_of_week  = number
    start_hour   = number
    start_minute = number
  })
  description = "Maintenance window configuration"
  default = {
    day_of_week  = 0  # Sunday
    start_hour   = 4  # 4 AM
    start_minute = 0
  }
}

# Database Configuration
variable "databases" {
  type        = list(string)
  description = "List of databases to create"
  default     = ["presence_prod", "postgres"]
}

variable "postgresql_configurations" {
  type        = map(string)
  description = "PostgreSQL server configurations"
  default = {
    "log_statement"                 = "ddl"
    "log_min_duration_statement"    = "5000"
    "log_connections"               = "on"
    "log_disconnections"            = "on"
    "connection_throttling"         = "on"
    "shared_preload_libraries"      = "pg_stat_statements"
    "max_connections"               = "200"
    "shared_buffers"                = "256MB"
    "effective_cache_size"          = "1GB"
    "maintenance_work_mem"          = "64MB"
    "checkpoint_completion_target"  = "0.9"
    "wal_buffers"                   = "16MB"
    "default_statistics_target"     = "100"
  }
}

# Tags
variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default = {
    Environment = "prod"
    Project     = "presence-cloud"
    ManagedBy   = "terraform"
    Compliance  = "required"
  }
}