@echo off
REM Simple batch script to get complete query by ID from Redshift Serverless
REM Usage: get_query_by_id.bat <query_id>
REM Example: get_query_by_id.bat 1224653

set QUERY_ID=%1

if "%QUERY_ID%"=="" (
    echo Usage: get_query_by_id.bat ^<query_id^>
    echo Example: get_query_by_id.bat 1224653
    pause
    exit /b 1
)

echo Getting complete query details for Query ID: %QUERY_ID%...

powershell.exe -ExecutionPolicy Bypass -File "get_query_by_id.ps1" -QueryId %QUERY_ID%

pause
