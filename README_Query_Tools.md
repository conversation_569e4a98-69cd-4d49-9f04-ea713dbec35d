# Redshift Serverless Query Tools

Complete solution for getting full query text from your Redshift Serverless instance `redshift-poc`.

## 🎯 Problem Solved
- **Query history shows truncated queries** ❌
- **Cannot see complete query text** ❌
- **Need to get specific query by ID** ❌

## ✅ Solution Provided
- **Get complete query history with full text** ✅
- **Get specific query details by ID** ✅
- **Export results to JSON and SQL files** ✅
- **Detailed performance metrics** ✅

---

## 📁 Files Created

### 1. Query History Tools
- `get_complete_query_history.ps1` - Main PowerShell script
- `get_query_history.bat` - Simple batch wrapper

### 2. Query by ID Tools
- `get_query_by_id.ps1` - Get specific query by ID
- `get_query_by_id.bat` - Simple batch wrapper

### 3. Documentation
- `README_Query_Tools.md` - This file

---

## 🚀 Usage Examples

### Get Query History (Last 7 days, limit 20)
```cmd
get_query_history.bat 7 20
```

### Get Query History (Default: last 1 day, limit 10)
```cmd
get_query_history.bat
```

### Get Specific Query by ID
```cmd
get_query_by_id.bat 1225488
```

### PowerShell Direct Usage
```powershell
# Query history
powershell -ExecutionPolicy Bypass -File "get_complete_query_history.ps1" -Days 7 -Limit 20

# Specific query
powershell -ExecutionPolicy Bypass -File "get_query_by_id.ps1" -QueryId 1225488

# Query history with specific query ID
powershell -ExecutionPolicy Bypass -File "get_complete_query_history.ps1" -QueryId 1225488
```

---

## 📊 What You Get

### Query History Output
```
=== COMPLETE QUERY HISTORY ===
Found 10 queries

Query ID: 1225488
Time: 2025-07-29 10:53:14.162058
Database: dev | Type: SELECT | Status: success
Duration: 9389 ms | Rows: 10 | Bytes: 3634
User: IAM:dev_azure_devops_user
Result: Success
Query:
WITH complete_queries AS ( SELECT h.query_id, h.start_time, h.end_time... [COMPLETE QUERY TEXT]
```

### Query by ID Output
```
COMPLETE QUERY DETAILS FOR ID: 1225488

QUERY INFORMATION:
Query ID: 1225488
Start Time: 2025-07-29 10:53:14.162058
End Time: 2025-07-29 10:53:23.551141
Database: dev
Query Type: SELECT
Status: success
User: IAM:dev_azure_devops_user

PERFORMANCE METRICS:
Duration: 9389 ms
Elapsed Time: 9389083 ms
Queue Time: N/A ms
Execution Time: 1832302 ms
Compile Time: 7942913 ms
Planning Time: 119622 ms
Lock Wait Time: 105 ms

RESULT INFORMATION:
Returned Rows: 10
Returned Bytes: 3634
Result: Success
Usage Limit: N/A
Compute Type: primary
Redshift Version: 1.0.117891

COMPLETE QUERY TEXT:
================================================================================
[COMPLETE UNTRUNCATED QUERY TEXT HERE]
================================================================================
```

---

## 📁 File Exports

### Automatic File Generation
- **JSON files**: Complete query details in JSON format
- **SQL files**: Just the query text for easy copying
- **Timestamped**: Files include date/time for organization

### File Naming Convention
- Query History: `query_history_YYYYMMDD_HHMMSS.json`
- Query by ID: `query_1225488_details_YYYYMMDD_HHMMSS.json`
- SQL Text: `query_1225488_YYYYMMDD_HHMMSS.sql`

---

## ⚙️ Configuration

### Default Settings
- **Workgroup**: `redshift-poc`
- **Database**: `dev`
- **Region**: `us-east-1`
- **Account**: `************`

### Customization
You can modify the scripts to change:
- Workgroup name
- Database name
- AWS region
- Time ranges
- Result limits

---

## 🔧 Technical Details

### How It Works
1. **Uses `sys_query_history`** for basic query information
2. **Uses `sys_child_query_text`** for complete query text (up to 65,535 characters)
3. **Combines both sources** using `COALESCE()` to get the longest available text
4. **Filters out utility queries** like SET statements
5. **Provides detailed performance metrics**

### Query Retention
- Redshift keeps query history for a limited time
- Older queries (like your 1224653) may no longer be available
- Recent queries will have complete text available

### Performance Metrics Included
- Duration (milliseconds)
- Elapsed time
- Queue time
- Execution time
- Compile time
- Planning time
- Lock wait time
- Rows returned
- Bytes returned

---

## 🎯 Use Cases

### 1. Debug Long Queries
```cmd
get_query_by_id.bat 1225488
```
Get complete query text and performance metrics for analysis.

### 2. Query Performance Analysis
```cmd
get_query_history.bat 1 50
```
Get last day's queries with performance details.

### 3. Find Recent Queries
```cmd
get_query_history.bat 7 100
```
Get last week's query history.

### 4. Export for Analysis
All results are automatically saved to JSON files for further analysis in Excel, Python, or other tools.

---

## 🚨 Troubleshooting

### Query Not Found
```
No query found with ID: 1224653
The query might be too old (outside retention period) or the ID might be incorrect.
```
**Solution**: The query is outside the retention period. Use recent query IDs.

### AWS CLI Not Configured
**Solution**: Run `aws configure` to set up your credentials.

### Permission Errors
**Solution**: Ensure your AWS user has Redshift Data API permissions.

---

## 🎉 Success!

Your query truncation issue is now **completely solved**! You can:
- ✅ Get complete query text (no more truncation)
- ✅ Search by specific query ID
- ✅ Get detailed performance metrics
- ✅ Export results for analysis
- ✅ Use simple batch commands or PowerShell

**No more truncated queries!** 🎊
