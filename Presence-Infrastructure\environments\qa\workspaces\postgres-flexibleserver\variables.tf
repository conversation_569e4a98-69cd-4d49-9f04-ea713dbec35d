# Basic Configuration Variables
variable "db_name" {
  type        = string
  description = "Name of the PostgreSQL Flexible Database Server"
}

variable "key_vault_name" {
  type        = string
  description = "Key Vault Name"
}

variable "db_username_secret" {
  type        = string
  description = "Database username secret name in Key Vault"
}

variable "db_password_secret" {
  type        = string
  description = "Database password secret name in Key Vault"
}

variable "location" {
  type        = string
  description = "Azure region where resources will be deployed"
}

variable "rg_name" {
  type        = string
  description = "Resource Group Name"
}

variable "db_version" {
  type        = string
  description = "PostgreSQL version"
  default     = "13"
}

# Server Configuration Variables
variable "sku_name" {
  type        = string
  description = "SKU name for the PostgreSQL server"
  default     = "GP_Standard_D2s_v3"  # General Purpose for QA
}

variable "storage_mb" {
  type        = number
  description = "Storage size in MB"
  default     = 65536  # 64 GB for QA
}

variable "storage_tier" {
  type        = string
  description = "Storage tier"
  default     = "P6"
}

variable "zone" {
  type        = number
  description = "Availability zone for the server"
  default     = 1
}

# Firewall Rules for Public Access (QA Environment)
variable "allowed_ip_ranges" {
  type = map(object({
    start_ip = string
    end_ip   = string
  }))
  description = "Map of allowed IP ranges for public access"
  default = {
    "office_network" = {
      start_ip = "***********"
      end_ip   = "*************"
    }
    "qa_testing_team" = {
      start_ip = "************"
      end_ip   = "**************"
    }
    "automation_tools" = {
      start_ip = "*********"
      end_ip   = "***********"
    }
  }
}

# Database Configuration
variable "databases" {
  type        = list(string)
  description = "List of databases to create"
  default     = ["presence_qa", "postgres"]
}

variable "postgresql_configurations" {
  type        = map(string)
  description = "PostgreSQL server configurations"
  default = {
    "log_statement"              = "mod"
    "log_min_duration_statement" = "2000"
    "log_connections"            = "on"
    "log_disconnections"         = "on"
    "connection_throttling"      = "on"
    "shared_preload_libraries"   = "pg_stat_statements"
  }
}

# Tags
variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default = {
    Environment = "qa"
    Project     = "presence-cloud"
    ManagedBy   = "terraform"
  }
}
