#!/usr/bin/env powershell

# SIMPLE SETUP SCRIPT - One-time setup for automatic query logging

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "   REDSHIFT QUERY LOGGING SETUP" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta
Write-Host ""

# Check if AWS CLI is configured
Write-Host "1. Checking AWS CLI configuration..." -ForegroundColor Yellow
try {
    $awsConfig = aws configure list 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   [OK] AWS CLI is configured" -ForegroundColor Green
    } else {
        Write-Host "   [ERROR] AWS CLI not configured" -ForegroundColor Red
        Write-Host "   Please run 'aws configure' first" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "   [ERROR] AWS CLI not found" -ForegroundColor Red
    Write-Host "   Please install AWS CLI first" -ForegroundColor Yellow
    exit 1
}

# Test Redshift connection
Write-Host "2. Testing Redshift connection..." -ForegroundColor Yellow
try {
    $testResult = aws redshift-serverless get-workgroup --workgroup-name redshift-poc --region us-east-1 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   [OK] Redshift connection successful" -ForegroundColor Green
    } else {
        Write-Host "   [ERROR] Cannot connect to Redshift" -ForegroundColor Red
        Write-Host "   Please check your AWS credentials and permissions" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "   [ERROR] Redshift connection failed" -ForegroundColor Red
    exit 1
}

# Create directory structure
Write-Host "3. Creating directory structure..." -ForegroundColor Yellow
$directories = @("query_logs", "query_logs\sql_files")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "   [OK] Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "   [OK] Directory exists: $dir" -ForegroundColor Green
    }
}

# Test the query logger
Write-Host "4. Testing query logger..." -ForegroundColor Yellow
try {
    # Run a quick test to make sure everything works
    $testSql = "SELECT 'Query Logger Test' as test_message, CURRENT_TIMESTAMP as test_time;"
    $result = aws redshift-data execute-statement --workgroup-name redshift-poc --database dev --sql $testSql --region us-east-1 | ConvertFrom-Json
    
    if ($result.Id) {
        Write-Host "   [OK] Query logger test successful" -ForegroundColor Green
    } else {
        Write-Host "   [ERROR] Query logger test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "   [WARN] Query logger test had issues" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   SETUP COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "WHAT'S BEEN SET UP:" -ForegroundColor Cyan
Write-Host "   [OK] Directory structure for logging" -ForegroundColor White
Write-Host "   [OK] Connection to Redshift verified" -ForegroundColor White
Write-Host ""

Write-Host "HOW TO USE:" -ForegroundColor Cyan
Write-Host "   1. Run: start_query_logger.bat" -ForegroundColor White
Write-Host "   2. The logger will run continuously and capture all queries" -ForegroundColor White
Write-Host ""

Write-Host "HOW TO SEARCH LOGGED QUERIES:" -ForegroundColor Cyan
Write-Host "   Search by text: .\query_search.ps1 -SearchTerm 'SELECT'" -ForegroundColor White
Write-Host "   Find by ID: .\query_search.ps1 -QueryId '1225488'" -ForegroundColor White
Write-Host ""

Write-Host "WHERE QUERIES ARE STORED:" -ForegroundColor Cyan
Write-Host "   Daily logs: query_logs\query_log_YYYYMMDD.log" -ForegroundColor White
Write-Host "   Individual SQL files: query_logs\sql_files\" -ForegroundColor White
Write-Host ""

Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "   1. Start the query logger now: start_query_logger.bat" -ForegroundColor White
Write-Host "   2. Let it run continuously to capture all queries" -ForegroundColor White
Write-Host "   3. Use query_search.ps1 to find any query later" -ForegroundColor White
Write-Host ""

Write-Host "You'll never lose query text again!" -ForegroundColor Green
