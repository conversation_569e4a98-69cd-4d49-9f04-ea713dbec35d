﻿-- Query ID: 1226142
-- Start Time: 2025-07-29 11:15:02.144275
-- End Time: N/A
-- Database: dev
-- Query Type: SELECT
-- Status: running
-- Duration: N/A ms
-- User: IAM:dev_azure_devops_user
-- Result: 
-- Logged: 07/29/2025 16:45:11
-- ================================================

WITH complete_queries AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        h.elapsed_time,
        h.queue_time,
        h.execution_time,
        h.compile_time,
        h.planning_time,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.query_id > 0
        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries
        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries
        AND h.query_text NOT LIKE 'SELECT pg_last_query_id%'  -- Exclude internal queries
        AND h.start_time >= CURRENT_DATE - INTERVAL '7 days'  -- Only recent queries
)
SELECT 
    query_id,
    start_time,
    end_time,
    database_name,
    query_type,
    status,
    duration_ms,
    elapsed_time,
    queue_time,
    execution_time,
    compile_time,
    planning_time,
    returned_rows,
    returned_bytes,
    username,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_queries
ORDER BY query_id ASC;
