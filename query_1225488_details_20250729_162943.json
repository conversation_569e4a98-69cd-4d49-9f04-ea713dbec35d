﻿{
    "Records":  [
                    [
                        {
                            "longValue":  1225488
                        },
                        {
                            "stringValue":  "2025-07-29 10:53:14.162058"
                        },
                        {
                            "stringValue":  "2025-07-29 10:53:23.551141"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  9389
                        },
                        {
                            "longValue":  9389083
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  1832302
                        },
                        {
                            "longValue":  7942913
                        },
                        {
                            "longValue":  119622
                        },
                        {
                            "longValue":  105
                        },
                        {
                            "longValue":  10
                        },
                        {
                            "longValue":  3634
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "primary"
                        },
                        {
                            "stringValue":  "1.0.117891                      "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_queries AS ( SELECT h.query_id, h.start_time, h.end_time, h.database_name, h.query_type, h.status, h.username, DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms, h.returned_rows, COALESCE(ct.complete_query_text, h.query_text) as full_query_text FROM sys_query_history h LEFT JOIN ( SELECT query_id, child_query_sequence, listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text FROM sys_child_query_text GROUP BY query_id, child_query_sequence ) ct ON h.query_id = ct.query_id WHERE h.start_time \u003e= CURRENT_DATE - INTERVAL \u00271 days\u0027 AND h.query_type NOT IN (\u0027UTILITY\u0027) ) SELECT query_id, start_time, database_name, query_type, status, duration_ms, returned_rows, username, full_query_text FROM complete_queries ORDER BY start_time DESC LIMIT 10;"
                        }
                    ]
                ],
    "ColumnMetadata":  [
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "query_id",
                               "length":  0,
                               "name":  "query_id",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "start_time",
                               "length":  0,
                               "name":  "start_time",
                               "nullable":  1,
                               "precision":  29,
                               "scale":  6,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "timestamp"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "end_time",
                               "length":  0,
                               "name":  "end_time",
                               "nullable":  1,
                               "precision":  29,
                               "scale":  6,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "timestamp"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "database_name",
                               "length":  0,
                               "name":  "database_name",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "query_type",
                               "length":  0,
                               "name":  "query_type",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "status",
                               "length":  0,
                               "name":  "status",
                               "nullable":  1,
                               "precision":  10,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "duration_ms",
                               "length":  0,
                               "name":  "duration_ms",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "elapsed_time",
                               "length":  0,
                               "name":  "elapsed_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "queue_time",
                               "length":  0,
                               "name":  "queue_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "execution_time",
                               "length":  0,
                               "name":  "execution_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "compile_time",
                               "length":  0,
                               "name":  "compile_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "planning_time",
                               "length":  0,
                               "name":  "planning_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "lock_wait_time",
                               "length":  0,
                               "name":  "lock_wait_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_rows",
                               "length":  0,
                               "name":  "returned_rows",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_bytes",
                               "length":  0,
                               "name":  "returned_bytes",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "username",
                               "length":  0,
                               "name":  "username",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "usage_limit",
                               "length":  0,
                               "name":  "usage_limit",
                               "nullable":  1,
                               "precision":  150,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "compute_type",
                               "length":  0,
                               "name":  "compute_type",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "redshift_version",
                               "length":  0,
                               "name":  "redshift_version",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "result",
                               "length":  0,
                               "name":  "result",
                               "nullable":  1,
                               "precision":  512,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "full_query_text",
                               "length":  0,
                               "name":  "full_query_text",
                               "nullable":  1,
                               "precision":  65535,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           }
                       ],
    "TotalNumRows":  1
}
