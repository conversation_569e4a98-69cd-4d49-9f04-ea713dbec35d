@echo off
REM Start the Automatic Query Logger
REM This will run continuously and log all queries with full text

echo ========================================
echo   REDSHIFT AUTOMATIC QUERY LOGGER
echo ========================================
echo.
echo This will start continuous logging of all Redshift queries.
echo All queries will be saved with complete text to prevent truncation.
echo.
echo Log Location: query_logs\
echo Check Interval: Every 5 minutes
echo.
echo Press Ctrl+C to stop the logger
echo.
pause

echo Starting Query Logger...
powershell.exe -ExecutionPolicy Bypass -File "auto_query_logger.ps1"

pause
