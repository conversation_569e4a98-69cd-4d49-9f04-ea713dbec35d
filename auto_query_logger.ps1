#!/usr/bin/env powershell

# AUTOMATIC QUERY LOGGER - Continuously captures all full queries
# This script runs continuously and logs every query with complete text
# Usage: .\auto_query_logger.ps1

param(
    [int]$IntervalMinutes = 5,  # How often to check for new queries
    [string]$WorkgroupName = "redshift-poc",
    [string]$Database = "dev",
    [string]$Region = "us-east-1",
    [string]$LogDirectory = "query_logs"
)

# Create log directory if it doesn't exist
if (!(Test-Path $LogDirectory)) {
    New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
    Write-Host "Created log directory: $LogDirectory" -ForegroundColor Green
}

# Initialize tracking file to remember last processed query
$trackingFile = "$LogDirectory\last_processed_query.txt"
$logFile = "$LogDirectory\query_log_$(Get-Date -Format 'yyyyMMdd').log"
$sqlLogDir = "$LogDirectory\sql_files"

if (!(Test-Path $sqlLogDir)) {
    New-Item -ItemType Directory -Path $sqlLogDir -Force | Out-Null
}

# Function to log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry -ForegroundColor $(if ($Level -eq "ERROR") { "Red" } elseif ($Level -eq "WARN") { "Yellow" } else { "Green" })
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Function to get last processed query ID
function Get-LastProcessedQueryId {
    if (Test-Path $trackingFile) {
        $lastId = Get-Content $trackingFile -ErrorAction SilentlyContinue
        if ($lastId -and $lastId -match '^\d+$') {
            return [long]$lastId
        }
    }
    return 0
}

# Function to update last processed query ID
function Set-LastProcessedQueryId {
    param([long]$QueryId)
    $QueryId | Out-File -FilePath $trackingFile -Encoding UTF8
}

# Function to capture new queries
function Capture-NewQueries {
    $lastProcessedId = Get-LastProcessedQueryId
    Write-Log "Checking for new queries since ID: $lastProcessedId"
    
    # SQL to get new queries with complete text
    $sql = @"
WITH complete_queries AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        h.elapsed_time,
        h.queue_time,
        h.execution_time,
        h.compile_time,
        h.planning_time,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.query_id > $lastProcessedId
        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries
        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries
        AND h.query_text NOT LIKE 'SELECT pg_last_query_id%'  -- Exclude internal queries
        AND h.start_time >= CURRENT_DATE - INTERVAL '7 days'  -- Only recent queries
)
SELECT 
    query_id,
    start_time,
    end_time,
    database_name,
    query_type,
    status,
    duration_ms,
    elapsed_time,
    queue_time,
    execution_time,
    compile_time,
    planning_time,
    returned_rows,
    returned_bytes,
    username,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_queries
ORDER BY query_id ASC;
"@

    try {
        # Execute the query
        $result = aws redshift-data execute-statement `
            --workgroup-name $WorkgroupName `
            --database $Database `
            --sql $sql `
            --region $Region | ConvertFrom-Json
        
        $statementId = $result.Id
        
        # Wait for query to complete
        do {
            Start-Sleep -Seconds 2
            $status = aws redshift-data describe-statement --id $statementId --region $Region | ConvertFrom-Json
        } while ($status.Status -eq "RUNNING" -or $status.Status -eq "SUBMITTED" -or $status.Status -eq "PICKED" -or $status.Status -eq "STARTED")
        
        if ($status.Status -eq "FINISHED") {
            if ($status.ResultRows -gt 0) {
                Write-Log "Found $($status.ResultRows) new queries to log"
                
                # Get the results
                $queryResults = aws redshift-data get-statement-result --id $statementId --region $Region | ConvertFrom-Json
                
                $newQueriesLogged = 0
                $latestQueryId = $lastProcessedId
                
                # Process each new query
                foreach ($record in $queryResults.Records) {
                    $queryId = $record[0].longValue
                    $startTime = $record[1].stringValue
                    $endTime = if ($record[2].stringValue) { $record[2].stringValue } else { "N/A" }
                    $database = $record[3].stringValue
                    $queryType = $record[4].stringValue
                    $status = $record[5].stringValue.Trim()
                    $durationMs = if ($record[6].longValue) { $record[6].longValue } else { "N/A" }
                    $elapsedTime = if ($record[7].longValue) { $record[7].longValue } else { "N/A" }
                    $queueTime = if ($record[8].longValue) { $record[8].longValue } else { "N/A" }
                    $executionTime = if ($record[9].longValue) { $record[9].longValue } else { "N/A" }
                    $compileTime = if ($record[10].longValue) { $record[10].longValue } else { "N/A" }
                    $planningTime = if ($record[11].longValue) { $record[11].longValue } else { "N/A" }
                    $returnedRows = if ($record[12].longValue) { $record[12].longValue } else { 0 }
                    $returnedBytes = if ($record[13].longValue) { $record[13].longValue } else { 0 }
                    $username = $record[14].stringValue.Trim()
                    $result = $record[15].stringValue
                    $fullQuery = $record[16].stringValue
                    
                    # Create detailed log entry
                    $logEntry = @"

========================================
QUERY ID: $queryId
========================================
Start Time: $startTime
End Time: $endTime
Database: $database
Query Type: $queryType
Status: $status
Duration: $durationMs ms
Elapsed Time: $elapsedTime ms
Queue Time: $queueTime ms
Execution Time: $executionTime ms
Compile Time: $compileTime ms
Planning Time: $planningTime ms
Returned Rows: $returnedRows
Returned Bytes: $returnedBytes
User: $username
Result: $result

COMPLETE QUERY TEXT:
----------------------------------------
$fullQuery
----------------------------------------

"@
                    
                    # Append to daily log file
                    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
                    
                    # Save individual SQL file
                    $sqlFileName = "$sqlLogDir\query_${queryId}_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
                    $sqlContent = @"
-- Query ID: $queryId
-- Start Time: $startTime
-- End Time: $endTime
-- Database: $database
-- Query Type: $queryType
-- Status: $status
-- Duration: $durationMs ms
-- User: $username
-- Result: $result
-- Logged: $(Get-Date)
-- ================================================

$fullQuery
"@
                    $sqlContent | Out-File -FilePath $sqlFileName -Encoding UTF8
                    
                    Write-Log "Logged Query ID: $queryId | Type: $queryType | Status: $status | User: $username"
                    
                    $newQueriesLogged++
                    if ($queryId -gt $latestQueryId) {
                        $latestQueryId = $queryId
                    }
                }
                
                # Update tracking file with latest query ID
                Set-LastProcessedQueryId $latestQueryId
                Write-Log "Successfully logged $newQueriesLogged new queries. Latest ID: $latestQueryId"
                
            } else {
                Write-Log "No new queries found"
            }
        } else {
            Write-Log "Query failed with status: $($status.Status)" "ERROR"
            if ($status.Error) {
                Write-Log "Error: $($status.Error)" "ERROR"
            }
        }
        
    } catch {
        Write-Log "Error capturing queries: $($_.Exception.Message)" "ERROR"
    }
}

# Main execution loop
Write-Log "=== AUTOMATIC QUERY LOGGER STARTED ==="
Write-Log "Workgroup: $WorkgroupName"
Write-Log "Database: $Database"
Write-Log "Region: $Region"
Write-Log "Check Interval: $IntervalMinutes minutes"
Write-Log "Log Directory: $LogDirectory"
Write-Log "Press Ctrl+C to stop"

try {
    while ($true) {
        Capture-NewQueries
        
        Write-Log "Waiting $IntervalMinutes minutes before next check..."
        Start-Sleep -Seconds ($IntervalMinutes * 60)
    }
} catch {
    Write-Log "Logger stopped: $($_.Exception.Message)" "WARN"
} finally {
    Write-Log "=== AUTOMATIC QUERY LOGGER STOPPED ==="
}
