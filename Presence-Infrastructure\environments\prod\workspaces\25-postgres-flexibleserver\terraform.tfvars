# PRODUCTION Environment Configuration - PRIVATE ACCESS
# Basic Configuration (Required Variables)
db_name              = "presence-postgres-prod"
key_vault_name       = "presence-kv-prod"
db_username_secret   = "postgres-admin-username"
db_password_secret   = "postgres-admin-password"
location             = "East US"
rg_name              = "presence-rg-prod"

# Network Configuration - PRIVATE ACCESS (Update with your actual values)
subnet_id                = "/subscriptions/YOUR_SUBSCRIPTION_ID/resourceGroups/presence-rg-prod/providers/Microsoft.Network/virtualNetworks/presence-vnet-prod/subnets/postgres-subnet"
vnet_id                  = "/subscriptions/YOUR_SUBSCRIPTION_ID/resourceGroups/presence-rg-prod/providers/Microsoft.Network/virtualNetworks/presence-vnet-prod"
private_dns_zone_name    = "presence-postgres-prod.private.postgres.database.azure.com"
dns_zone_link_name       = "presence-postgres-prod-dns-link"

# Note: Other variables have sensible defaults defined in variables.tf
# You can override them here if needed:
# sku_name      = "GP_Standard_D4s_v3"  # Default is already set
# storage_mb    = 131072                # Default is already set
# storage_tier  = "P10"                 # Default is already set
# zone          = 1                     # Default is already set
