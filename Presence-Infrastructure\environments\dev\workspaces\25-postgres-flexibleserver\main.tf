module "postgres_flexible_server" {
  source = "../../../../modules/postgres-flexible"

  # Basic Configuration
  db_name            = var.db_name
  key_vault_name     = var.key_vault_name
  db_username_secret = var.db_username_secret
  db_password_secret = var.db_password_secret
  location           = var.location
  rg_name            = var.rg_name
  db_version         = var.db_version

  # Network Configuration - PUBLIC ACCESS for DEV
  private_access_enabled        = false
  public_network_access_enabled = true
  subnet_id                     = null
  vnet_id                       = null
  private_dns_zone_name         = null
  dns_zone_link_name            = null

  # Server Configuration
  sku_name     = var.sku_name
  storage_mb   = var.storage_mb
  storage_tier = var.storage_tier
  zone         = var.zone

  # High Availability - Disabled for DEV
  high_availability_enabled = false
  standby_availability_zone = null

  # Backup Configuration - Basic for DEV
  backup_retention_days        = 7
  geo_redundant_backup_enabled = false

  # Maintenance Window - Weekend for DEV
  maintenance_window = {
    day_of_week  = 0  # Sunday
    start_hour   = 2
    start_minute = 0
  }

  # Firewall Rules for Public Access
  allow_azure_services = true
  allowed_ip_ranges = var.allowed_ip_ranges

  # Database Configuration
  databases = var.databases
  postgresql_configurations = var.postgresql_configurations

  # Tags
  tags = merge(var.tags, {
    Environment = "dev"
    AccessType  = "public"
  })
}