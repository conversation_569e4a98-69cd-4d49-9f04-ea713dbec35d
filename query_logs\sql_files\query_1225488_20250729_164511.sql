﻿-- Query ID: 1225488
-- Start Time: 2025-07-29 10:53:14.162058
-- End Time: 2025-07-29 10:53:23.551141
-- Database: dev
-- Query Type: SELECT
-- Status: success
-- Duration: 9389 ms
-- User: IAM:dev_azure_devops_user
-- Result: 
-- Logged: 07/29/2025 16:45:11
-- ================================================

WITH complete_queries AS ( SELECT h.query_id, h.start_time, h.end_time, h.database_name, h.query_type, h.status, h.username, DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms, h.returned_rows, COALESCE(ct.complete_query_text, h.query_text) as full_query_text FROM sys_query_history h LEFT JOIN ( SELECT query_id, child_query_sequence, listagg(text, '') within group (order by sequence) as complete_query_text FROM sys_child_query_text GROUP BY query_id, child_query_sequence ) ct ON h.query_id = ct.query_id WHERE h.start_time >= CURRENT_DATE - INTERVAL '1 days' AND h.query_type NOT IN ('UTILITY') ) SELECT query_id, start_time, database_name, query_type, status, duration_ms, returned_rows, username, full_query_text FROM complete_queries ORDER BY start_time DESC LIMIT 10;
