# Data sources for Key Vault secrets
data "azurerm_key_vault" "key_vault" {
  name                = var.key_vault_name
  resource_group_name = var.rg_name
}

data "azurerm_key_vault_secret" "db_username" {
  name         = var.db_username_secret
  key_vault_id = data.azurerm_key_vault.key_vault.id
}

data "azurerm_key_vault_secret" "db_password" {
  name         = var.db_password_secret
  key_vault_id = data.azurerm_key_vault.key_vault.id
}

# Private DNS Zone for private access
resource "azurerm_private_dns_zone" "postgres_dns_zone" {
  count               = var.private_access_enabled ? 1 : 0
  name                = var.private_dns_zone_name
  resource_group_name = var.rg_name

  tags = var.tags
}

# Private DNS Zone Virtual Network Link
resource "azurerm_private_dns_zone_virtual_network_link" "postgres_dns_zone_link" {
  count                 = var.private_access_enabled ? 1 : 0
  name                  = var.dns_zone_link_name
  private_dns_zone_name = azurerm_private_dns_zone.postgres_dns_zone[0].name
  virtual_network_id    = var.vnet_id
  resource_group_name   = var.rg_name
  registration_enabled  = false

  tags = var.tags
}

# PostgreSQL Flexible Server
resource "azurerm_postgresql_flexible_server" "postgres_server" {
  name                          = var.db_name
  resource_group_name           = var.rg_name
  location                      = var.location
  version                       = var.db_version
  public_network_access_enabled = var.public_network_access_enabled
  administrator_login           = data.azurerm_key_vault_secret.db_username.value
  administrator_password        = data.azurerm_key_vault_secret.db_password.value
  zone                          = var.zone
  storage_mb                    = var.storage_mb
  storage_tier                  = var.storage_tier
  sku_name                      = var.sku_name

  # Private access configuration
  delegated_subnet_id = var.private_access_enabled ? var.subnet_id : null
  private_dns_zone_id = var.private_access_enabled ? azurerm_private_dns_zone.postgres_dns_zone[0].id : null

  # High availability configuration for production
  dynamic "high_availability" {
    for_each = var.high_availability_enabled ? [1] : []
    content {
      mode                      = "ZoneRedundant"
      standby_availability_zone = var.standby_availability_zone
    }
  }

  # Backup configuration
  backup_retention_days        = var.backup_retention_days
  geo_redundant_backup_enabled = var.geo_redundant_backup_enabled

  # Maintenance window
  dynamic "maintenance_window" {
    for_each = var.maintenance_window != null ? [var.maintenance_window] : []
    content {
      day_of_week  = maintenance_window.value.day_of_week
      start_hour   = maintenance_window.value.start_hour
      start_minute = maintenance_window.value.start_minute
    }
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      zone,
      high_availability[0].standby_availability_zone,
    ]
  }
}

# Firewall rules for public access (dev and qa environments)
resource "azurerm_postgresql_flexible_server_firewall_rule" "allow_azure_services" {
  count            = var.public_network_access_enabled && var.allow_azure_services ? 1 : 0
  name             = "AllowAzureServices"
  server_id        = azurerm_postgresql_flexible_server.postgres_server.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

resource "azurerm_postgresql_flexible_server_firewall_rule" "allowed_ip_ranges" {
  for_each = var.public_network_access_enabled ? var.allowed_ip_ranges : {}

  name             = each.key
  server_id        = azurerm_postgresql_flexible_server.postgres_server.id
  start_ip_address = each.value.start_ip
  end_ip_address   = each.value.end_ip
}

# Database creation
resource "azurerm_postgresql_flexible_server_database" "databases" {
  for_each = toset(var.databases)

  name      = each.value
  server_id = azurerm_postgresql_flexible_server.postgres_server.id
  collation = "en_US.utf8"
  charset   = "utf8"
}

# PostgreSQL configurations
resource "azurerm_postgresql_flexible_server_configuration" "postgres_config" {
  for_each = var.postgresql_configurations

  name      = each.key
  server_id = azurerm_postgresql_flexible_server.postgres_server.id
  value     = each.value
}
