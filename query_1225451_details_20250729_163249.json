﻿{
    "Records":  [
                    [
                        {
                            "longValue":  1225451
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:50.355407"
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:55.696909"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  5341
                        },
                        {
                            "longValue":  5341502
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  639967
                        },
                        {
                            "longValue":  5030388
                        },
                        {
                            "longValue":  80378
                        },
                        {
                            "longValue":  57
                        },
                        {
                            "longValue":  17
                        },
                        {
                            "longValue":  2679
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "primary"
                        },
                        {
                            "stringValue":  "1.0.117891                      "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT query_id, start_time, database_name, query_type, status, DATEDIFF(millisecond, start_time, end_time) as duration_ms, query_text FROM sys_query_history WHERE start_time \u003e= CURRENT_DATE - INTERVAL \u00277 days\u0027 ORDER BY start_time DESC LIMIT 20;"
                        }
                    ]
                ],
    "ColumnMetadata":  [
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "query_id",
                               "length":  0,
                               "name":  "query_id",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "start_time",
                               "length":  0,
                               "name":  "start_time",
                               "nullable":  1,
                               "precision":  29,
                               "scale":  6,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "timestamp"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "end_time",
                               "length":  0,
                               "name":  "end_time",
                               "nullable":  1,
                               "precision":  29,
                               "scale":  6,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "timestamp"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "database_name",
                               "length":  0,
                               "name":  "database_name",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "query_type",
                               "length":  0,
                               "name":  "query_type",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "status",
                               "length":  0,
                               "name":  "status",
                               "nullable":  1,
                               "precision":  10,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "duration_ms",
                               "length":  0,
                               "name":  "duration_ms",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "elapsed_time",
                               "length":  0,
                               "name":  "elapsed_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "queue_time",
                               "length":  0,
                               "name":  "queue_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "execution_time",
                               "length":  0,
                               "name":  "execution_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "compile_time",
                               "length":  0,
                               "name":  "compile_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "planning_time",
                               "length":  0,
                               "name":  "planning_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "lock_wait_time",
                               "length":  0,
                               "name":  "lock_wait_time",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_rows",
                               "length":  0,
                               "name":  "returned_rows",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_bytes",
                               "length":  0,
                               "name":  "returned_bytes",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "username",
                               "length":  0,
                               "name":  "username",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "usage_limit",
                               "length":  0,
                               "name":  "usage_limit",
                               "nullable":  1,
                               "precision":  150,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "compute_type",
                               "length":  0,
                               "name":  "compute_type",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "redshift_version",
                               "length":  0,
                               "name":  "redshift_version",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "result",
                               "length":  0,
                               "name":  "result",
                               "nullable":  1,
                               "precision":  512,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "full_query_text",
                               "length":  0,
                               "name":  "full_query_text",
                               "nullable":  1,
                               "precision":  65535,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           }
                       ],
    "TotalNumRows":  1
}
