#!/usr/bin/env powershell

# Script to get complete query history from Redshift Serverless with full query text
# Usage: .\get_complete_query_history.ps1 [days] [limit]
# Example: .\get_complete_query_history.ps1 7 20

param(
    [int]$Days = 1,
    [int]$Limit = 10,
    [string]$WorkgroupName = "redshift-poc",
    [string]$Database = "dev",
    [string]$Region = "us-east-1"
)

Write-Host "Getting complete query history for last $Days days (limit: $Limit queries)..." -ForegroundColor Green

# SQL query to get complete query history with full query text
$sql = @"
WITH complete_queries AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.start_time >= CURRENT_DATE - INTERVAL '$Days days'
        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries like SET statements
        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries
)
SELECT 
    query_id,
    start_time,
    database_name,
    query_type,
    status,
    duration_ms,
    returned_rows,
    returned_bytes,
    username,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_queries
ORDER BY start_time DESC
LIMIT $Limit;
"@

try {
    Write-Host "Executing query on Redshift Serverless..." -ForegroundColor Yellow
    
    # Execute the query
    $result = aws redshift-data execute-statement `
        --workgroup-name $WorkgroupName `
        --database $Database `
        --sql $sql `
        --region $Region | ConvertFrom-Json
    
    $statementId = $result.Id
    Write-Host "Query submitted with ID: $statementId" -ForegroundColor Cyan
    
    # Wait for query to complete
    do {
        Start-Sleep -Seconds 2
        $status = aws redshift-data describe-statement --id $statementId --region $Region | ConvertFrom-Json
        Write-Host "Query status: $($status.Status)" -ForegroundColor Yellow
    } while ($status.Status -eq "RUNNING" -or $status.Status -eq "SUBMITTED" -or $status.Status -eq "PICKED")
    
    if ($status.Status -eq "FINISHED") {
        Write-Host "Query completed successfully!" -ForegroundColor Green
        Write-Host "Fetching results..." -ForegroundColor Yellow
        
        # Get the results
        $queryResults = aws redshift-data get-statement-result --id $statementId --region $Region | ConvertFrom-Json
        
        Write-Host "`n=== COMPLETE QUERY HISTORY ===" -ForegroundColor Magenta
        Write-Host "Found $($queryResults.TotalNumRows) queries" -ForegroundColor Green
        Write-Host "=" * 80 -ForegroundColor Magenta
        
        # Display results in a formatted way
        foreach ($record in $queryResults.Records) {
            $queryId = $record[0].longValue
            $startTime = $record[1].stringValue
            $database = $record[2].stringValue
            $queryType = $record[3].stringValue
            $status = $record[4].stringValue.Trim()
            $durationMs = if ($record[5].longValue) { $record[5].longValue } else { "N/A" }
            $returnedRows = if ($record[6].longValue) { $record[6].longValue } else { 0 }
            $returnedBytes = if ($record[7].longValue) { $record[7].longValue } else { 0 }
            $username = $record[8].stringValue.Trim()
            $result = $record[9].stringValue
            $fullQuery = $record[10].stringValue
            
            Write-Host "`nQuery ID: $queryId" -ForegroundColor Cyan
            Write-Host "Time: $startTime" -ForegroundColor Gray
            Write-Host "Database: $database | Type: $queryType | Status: $status" -ForegroundColor Gray
            Write-Host "Duration: $durationMs ms | Rows: $returnedRows | Bytes: $returnedBytes" -ForegroundColor Gray
            Write-Host "User: $username" -ForegroundColor Gray
            Write-Host "Result: $result" -ForegroundColor $(if ($status -eq "success") { "Green" } else { "Red" })
            Write-Host "Query:" -ForegroundColor Yellow
            Write-Host $fullQuery -ForegroundColor White
            Write-Host "-" * 80 -ForegroundColor Gray
        }
        
        # Save to file
        $outputFile = "query_history_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        $queryResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputFile -Encoding UTF8
        Write-Host "`nResults also saved to: $outputFile" -ForegroundColor Green
        
    } else {
        Write-Host "Query failed with status: $($status.Status)" -ForegroundColor Red
        if ($status.Error) {
            Write-Host "Error: $($status.Error)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nScript completed!" -ForegroundColor Green
