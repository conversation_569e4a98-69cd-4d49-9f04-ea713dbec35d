# 🚀 AUTOMATIC REDSHIFT QUERY LOGGING SOLUTION

**PROBLEM SOLVED**: Never lose complete query text again! This solution automatically captures and logs every Redshift query with full text before it gets truncated or ages out.

## 🎯 What This Solves

- ❌ **Query history shows truncated queries**
- ❌ **Queries age out of system tables** 
- ❌ **Cannot find old queries**
- ❌ **Manual backup is tedious**

## ✅ What You Get

- ✅ **Automatic continuous logging** of all queries
- ✅ **Complete query text** (no truncation)
- ✅ **Permanent storage** (never ages out)
- ✅ **Fast search capabilities**
- ✅ **Individual SQL files** for each query
- ✅ **Detailed performance metrics**

---

## 🏗️ QUICK SETUP (One-Time)

### Step 1: Run Setup
```cmd
powershell -ExecutionPolicy Bypass -File "setup_query_logging.ps1"
```

### Step 2: Start Logger
```cmd
start_query_logger.bat
```

**That's it!** The logger will now run continuously and capture every query.

---

## 📁 Files Created

### Core System
- `auto_query_logger.ps1` - Main logging engine
- `start_query_logger.bat` - Easy startup script
- `query_search.ps1` - Search through logged queries
- `setup_query_logging.ps1` - One-time setup script

### Directories Created
- `query_logs/` - Main log directory
- `query_logs/sql_files/` - Individual SQL files
- `query_logs/daily_logs/` - Daily log files

---

## 🎮 HOW TO USE

### Start Automatic Logging
```cmd
# Option 1: Use desktop shortcut
Double-click "Start Query Logger" on desktop

# Option 2: Use batch file
start_query_logger.bat

# Option 3: Direct PowerShell
powershell -ExecutionPolicy Bypass -File "auto_query_logger.ps1"
```

### Search Your Queries
```cmd
# Find queries containing specific text
.\query_search.ps1 -SearchTerm "SELECT * FROM users"

# Find specific query by ID
.\query_search.ps1 -QueryId "1225488"

# Search in last 30 days
.\query_search.ps1 -SearchTerm "CREATE TABLE" -Days 30
```

### Get Recent Query History
```cmd
# Get last day's queries
get_query_history.bat

# Get specific query details
get_query_by_id.bat 1225488
```

---

## 📊 What Gets Logged

### For Each Query:
- **Query ID** - Unique identifier
- **Complete Query Text** - Full SQL (no truncation!)
- **Timestamps** - Start and end times
- **Performance Metrics** - Duration, queue time, execution time
- **User Information** - Who ran the query
- **Status** - Success/failure with error messages
- **Result Metrics** - Rows returned, bytes processed

### File Formats:
1. **Daily Log Files** - Human-readable format
2. **Individual SQL Files** - One file per query
3. **JSON Exports** - Machine-readable format

---

## 🔍 SEARCH EXAMPLES

### Find All SELECT Queries
```cmd
.\query_search.ps1 -SearchTerm "SELECT"
```

### Find Failed Queries
```cmd
.\query_search.ps1 -SearchTerm "failed"
```

### Find Queries by User
```cmd
.\query_search.ps1 -SearchTerm "anilkumar.adada"
```

### Find CREATE TABLE Statements
```cmd
.\query_search.ps1 -SearchTerm "CREATE TABLE"
```

### Get Specific Query Details
```cmd
.\query_search.ps1 -QueryId "1225488"
```

---

## 📁 File Organization

```
query_logs/
├── query_log_20250729.log          # Daily log file
├── last_processed_query.txt        # Tracking file
├── sql_files/                      # Individual SQL files
│   ├── query_1225488_20250729_163045.sql
│   ├── query_1225489_20250729_163102.sql
│   └── ...
└── daily_logs/                     # Archive of daily logs
    ├── query_log_20250728.log
    └── query_log_20250727.log
```

---

## ⚙️ Configuration

### Default Settings
- **Check Interval**: Every 5 minutes
- **Workgroup**: redshift-poc
- **Database**: dev
- **Region**: us-east-1

### Customize Settings
Edit the parameters in `auto_query_logger.ps1`:
```powershell
param(
    [int]$IntervalMinutes = 5,        # Change check frequency
    [string]$WorkgroupName = "redshift-poc",
    [string]$Database = "dev",
    [string]$Region = "us-east-1"
)
```

---

## 🔧 Advanced Usage

### Run as Background Service
```cmd
# Start in background (minimized)
start /min start_query_logger.bat
```

### Custom Log Directory
```powershell
.\auto_query_logger.ps1 -LogDirectory "C:\MyQueryLogs"
```

### Different Check Interval
```powershell
.\auto_query_logger.ps1 -IntervalMinutes 2  # Check every 2 minutes
```

---

## 📈 Monitoring

### Check Logger Status
- Look for the PowerShell window running the logger
- Check `query_logs/query_log_YYYYMMDD.log` for recent entries
- Verify new SQL files in `query_logs/sql_files/`

### Log File Contents
Each log entry shows:
```
========================================
QUERY ID: 1225488
========================================
Start Time: 2025-07-29 10:53:14.162058
End Time: 2025-07-29 10:53:23.551141
Database: dev
Query Type: SELECT
Status: success
Duration: 9389 ms
...
COMPLETE QUERY TEXT:
----------------------------------------
SELECT * FROM users WHERE id = 123;
----------------------------------------
```

---

## 🚨 Troubleshooting

### Logger Not Starting
1. Check AWS CLI configuration: `aws configure list`
2. Verify Redshift permissions
3. Run setup again: `.\setup_query_logging.ps1`

### No Queries Being Logged
1. Make sure queries are being run in the correct workgroup
2. Check the log file for error messages
3. Verify the logger is still running

### Search Not Finding Queries
1. Check if the logger has been running
2. Verify queries exist in `query_logs/sql_files/`
3. Try broader search terms

---

## 🎯 BENEFITS

### Never Lose Queries Again
- **Permanent Storage** - Queries saved forever
- **Complete Text** - No more truncation
- **Automatic** - No manual intervention needed

### Fast Query Retrieval
- **Instant Search** - Find any query in seconds
- **Multiple Formats** - SQL files, logs, JSON
- **Rich Metadata** - Performance metrics included

### Production Ready
- **Continuous Operation** - Runs 24/7
- **Error Handling** - Robust error recovery
- **Scalable** - Handles high query volumes

---

## 🎉 SUCCESS!

**Your query truncation problem is now PERMANENTLY SOLVED!**

✅ **Automatic logging** captures every query  
✅ **Complete query text** preserved forever  
✅ **Fast search** finds any query instantly  
✅ **Zero maintenance** once set up  

**You'll never lose a query again!** 🚀
