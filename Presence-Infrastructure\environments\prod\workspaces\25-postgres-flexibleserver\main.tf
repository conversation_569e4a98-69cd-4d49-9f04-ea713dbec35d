module "postgres_flexible_server" {
  source = "../../../../modules/postgres-flexible"

  # Basic Configuration
  db_name            = var.db_name
  key_vault_name     = var.key_vault_name
  db_username_secret = var.db_username_secret
  db_password_secret = var.db_password_secret
  location           = var.location
  rg_name            = var.rg_name
  db_version         = var.db_version

  # Network Configuration - PRIVATE ACCESS for PRODUCTION
  private_access_enabled        = true
  public_network_access_enabled = false
  subnet_id                     = var.subnet_id
  vnet_id                       = var.vnet_id
  private_dns_zone_name         = var.private_dns_zone_name
  dns_zone_link_name            = var.dns_zone_link_name

  # Server Configuration - Production specs
  sku_name     = var.sku_name
  storage_mb   = var.storage_mb
  storage_tier = var.storage_tier
  zone         = var.zone

  # High Availability - ENABLED for PRODUCTION
  high_availability_enabled = var.high_availability_enabled
  standby_availability_zone = var.standby_availability_zone

  # Backup Configuration - Enhanced for PRODUCTION
  backup_retention_days        = var.backup_retention_days
  geo_redundant_backup_enabled = var.geo_redundant_backup_enabled

  # Maintenance Window - Scheduled for PRODUCTION
  maintenance_window = var.maintenance_window

  # Firewall Rules - NONE for private access
  allow_azure_services = false
  allowed_ip_ranges = {}

  # Database Configuration
  databases = var.databases
  postgresql_configurations = var.postgresql_configurations

  # Tags
  tags = merge(var.tags, {
    Environment = "prod"
    AccessType  = "private"
    Compliance  = "required"
  })
}