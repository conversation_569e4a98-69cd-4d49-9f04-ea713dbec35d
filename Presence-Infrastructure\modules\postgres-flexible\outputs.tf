output "postgresql_server_id" {
  description = "The ID of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres_server.id
}

output "postgresql_server_name" {
  description = "The name of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres_server.name
}

output "postgresql_fqdn" {
  description = "The fully qualified domain name of the PostgreSQL server"
  value       = azurerm_postgresql_flexible_server.postgres_server.fqdn
}

output "postgresql_administrator_login" {
  description = "The administrator login name for the PostgreSQL server"
  value       = azurerm_postgresql_flexible_server.postgres_server.administrator_login
  sensitive   = true
}

output "connection_string" {
  description = "PostgreSQL connection string"
  value       = "postgresql://${azurerm_postgresql_flexible_server.postgres_server.administrator_login}@${azurerm_postgresql_flexible_server.postgres_server.fqdn}:5432/postgres?sslmode=require"
  sensitive   = true
}

output "private_dns_zone_name" {
  description = "The private DNS zone name used for PostgreSQL"
  value       = var.private_access_enabled ? azurerm_private_dns_zone.postgres_dns_zone[0].name : null
}

output "private_dns_zone_id" {
  description = "The private DNS zone ID used for PostgreSQL"
  value       = var.private_access_enabled ? azurerm_private_dns_zone.postgres_dns_zone[0].id : null
}

output "public_network_access_enabled" {
  description = "Whether public network access is enabled"
  value       = azurerm_postgresql_flexible_server.postgres_server.public_network_access_enabled
}

output "server_version" {
  description = "The version of the PostgreSQL server"
  value       = azurerm_postgresql_flexible_server.postgres_server.version
}

output "databases_created" {
  description = "List of databases created"
  value       = keys(azurerm_postgresql_flexible_server_database.databases)
}
