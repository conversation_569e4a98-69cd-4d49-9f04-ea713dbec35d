﻿{
    "Records":  [
                    [
                        {
                            "longValue":  1225784
                        },
                        {
                            "stringValue":  "2025-07-29 11:01:39.196054"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "running   "
                        },
                        {
                            "isNull":  true
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_queries AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.start_time \u003e= CURRENT_DATE - INTERVAL \u00271 days\u0027\n        AND h.query_type NOT IN (\u0027UTILITY\u0027)  -- Exclude utility queries like SET statements\n        AND h.query_text NOT LIKE \u0027SET application_name%\u0027  -- Exclude Data API internal queries\n)\nSELECT \n    query_id,\n    start_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    returned_rows,\n    returned_bytes,\n    username,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE \u0027Success\u0027\n    END as result,\n    full_query_text\nFROM complete_queries\nORDER BY start_time DESC\nLIMIT 20;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225776
                        },
                        {
                            "stringValue":  "2025-07-29 11:01:26.990358"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225773
                        },
                        {
                            "stringValue":  "2025-07-29 11:01:24.952644"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1932
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_query AS (\\n    SELECT \\n        h.query_id,\\n        h.start_time,\\n        h.end_time,\\n        h.database_name,\\n        h.query_type,\\n        h.status,\\n        h.username,\\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\\n        h.returned_rows,\\n        h.returned_bytes,\\n        h.error_message,\\n        h.elapsed_time,\\n        h.queue_time,\\n        h.execution_time,\\n        h.compile_time,\\n        h.planning_time,\\n        h.lock_wait_time,\\n        h.usage_limit,\\n        h.compute_type,\\n        h.redshift_version,\\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\\n    FROM sys_query_history h\\n    LEFT JOIN (\\n        SELECT \\n            query_id,\\n            child_query_sequence,\\n            listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text\\n        FROM sys_child_query_text\\n        GROUP BY query_id, child_query_sequence\\n    ) ct ON h.query_id = ct.query_id\\n    WHERE h.query_id = 1073946732\\n)\\nSELECT \\n    query_id,\\n    start_time,\\n    end_time,\\n    database_name,\\n    query_type,\\n    status,\\n    duration_ms,\\n    elapsed_time,\\n    queue_time,\\n    execution_time,\\n    compile_time,\\n    planning_time,\\n    lock_wait_time,\\n    returned_rows,\\n    returned_bytes,\\n    username,\\n    usage_limit,\\n    compute_type,\\n    redshift_version,\\n    CASE \\n        WHEN error_message IS NOT NULL THEN error_message\\n        ELSE \u0027Success\u0027\\n    END as result,\\n    full_query_text\\nFROM complete_query;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225689
                        },
                        {
                            "stringValue":  "2025-07-29 10:59:59.919271"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225686
                        },
                        {
                            "stringValue":  "2025-07-29 10:59:58.310477"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1500
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_query AS (\\n    SELECT \\n        h.query_id,\\n        h.start_time,\\n        h.end_time,\\n        h.database_name,\\n        h.query_type,\\n        h.status,\\n        h.username,\\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\\n        h.returned_rows,\\n        h.returned_bytes,\\n        h.error_message,\\n        h.elapsed_time,\\n        h.queue_time,\\n        h.execution_time,\\n        h.compile_time,\\n        h.planning_time,\\n        h.lock_wait_time,\\n        h.usage_limit,\\n        h.compute_type,\\n        h.redshift_version,\\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\\n    FROM sys_query_history h\\n    LEFT JOIN (\\n        SELECT \\n            query_id,\\n            child_query_sequence,\\n            listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text\\n        FROM sys_child_query_text\\n        GROUP BY query_id, child_query_sequence\\n    ) ct ON h.query_id = ct.query_id\\n    WHERE h.query_id = 1224653\\n)\\nSELECT \\n    query_id,\\n    start_time,\\n    end_time,\\n    database_name,\\n    query_type,\\n    status,\\n    duration_ms,\\n    elapsed_time,\\n    queue_time,\\n    execution_time,\\n    compile_time,\\n    planning_time,\\n    lock_wait_time,\\n    returned_rows,\\n    returned_bytes,\\n    username,\\n    usage_limit,\\n    compute_type,\\n    redshift_version,\\n    CASE \\n        WHEN error_message IS NOT NULL THEN error_message\\n        ELSE \u0027Success\u0027\\n    END as result,\\n    full_query_text\\nFROM complete_query;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225679
                        },
                        {
                            "stringValue":  "2025-07-29 10:59:42.828946"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225674
                        },
                        {
                            "stringValue":  "2025-07-29 10:59:36.419505"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  6347
                        },
                        {
                            "longValue":  1
                        },
                        {
                            "longValue":  1106
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_query AS (\\n    SELECT \\n        h.query_id,\\n        h.start_time,\\n        h.end_time,\\n        h.database_name,\\n        h.query_type,\\n        h.status,\\n        h.username,\\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\\n        h.returned_rows,\\n        h.returned_bytes,\\n        h.error_message,\\n        h.elapsed_time,\\n        h.queue_time,\\n        h.execution_time,\\n        h.compile_time,\\n        h.planning_time,\\n        h.lock_wait_time,\\n        h.usage_limit,\\n        h.compute_type,\\n        h.redshift_version,\\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\\n    FROM sys_query_history h\\n    LEFT JOIN (\\n        SELECT \\n            query_id,\\n            child_query_sequence,\\n            listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text\\n        FROM sys_child_query_text\\n        GROUP BY query_id, child_query_sequence\\n    ) ct ON h.query_id = ct.query_id\\n    WHERE h.query_id = 1225488\\n)\\nSELECT \\n    query_id,\\n    start_time,\\n    end_time,\\n    database_name,\\n    query_type,\\n    status,\\n    duration_ms,\\n    elapsed_time,\\n    queue_time,\\n    execution_time,\\n    compile_time,\\n    planning_time,\\n    lock_wait_time,\\n    returned_rows,\\n    returned_bytes,\\n    username,\\n    usage_limit,\\n    compute_type,\\n    redshift_version,\\n    CASE \\n        WHEN error_message IS NOT NULL THEN error_message\\n        ELSE \u0027Success\u0027\\n    END as result,\\n    full_query_text\\nFROM complete_query;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225556
                        },
                        {
                            "stringValue":  "2025-07-29 10:55:08.726612"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225551
                        },
                        {
                            "stringValue":  "2025-07-29 10:55:01.007067"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  7626
                        },
                        {
                            "longValue":  5
                        },
                        {
                            "longValue":  3564
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_queries AS (\\n    SELECT \\n        h.query_id,\\n        h.start_time,\\n        h.end_time,\\n        h.database_name,\\n        h.query_type,\\n        h.status,\\n        h.username,\\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\\n        h.returned_rows,\\n        h.returned_bytes,\\n        h.error_message,\\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\\n    FROM sys_query_history h\\n    LEFT JOIN (\\n        SELECT \\n            query_id,\\n            child_query_sequence,\\n            listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text\\n        FROM sys_child_query_text\\n        GROUP BY query_id, child_query_sequence\\n    ) ct ON h.query_id = ct.query_id\\n    WHERE h.start_time \u003e= CURRENT_DATE - INTERVAL \u00271 days\u0027\\n        AND h.query_type NOT IN (\u0027UTILITY\u0027)  -- Exclude utility queries like SET statements\\n        AND h.query_text NOT LIKE \u0027SET application_name%\u0027  -- Exclude Data API internal queries\\n)\\nSELECT \\n    query_id,\\n    start_time,\\n    database_name,\\n    query_type,\\n    status,\\n    duration_ms,\\n    returned_rows,\\n    returned_bytes,\\n    username,\\n    CASE \\n        WHEN error_message IS NOT NULL THEN error_message\\n        ELSE \u0027Success\u0027\\n    END as result,\\n    full_query_text\\nFROM complete_queries\\nORDER BY start_time DESC\\nLIMIT 5;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225497
                        },
                        {
                            "stringValue":  "2025-07-29 10:53:23.613838"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225488
                        },
                        {
                            "stringValue":  "2025-07-29 10:53:14.162058"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  9389
                        },
                        {
                            "longValue":  10
                        },
                        {
                            "longValue":  3634
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "WITH complete_queries AS ( SELECT h.query_id, h.start_time, h.end_time, h.database_name, h.query_type, h.status, h.username, DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms, h.returned_rows, COALESCE(ct.complete_query_text, h.query_text) as full_query_text FROM sys_query_history h LEFT JOIN ( SELECT query_id, child_query_sequence, listagg(text, \u0027\u0027) within group (order by sequence) as complete_query_text FROM sys_child_query_text GROUP BY query_id, child_query_sequence ) ct ON h.query_id = ct.query_id WHERE h.start_time \u003e= CURRENT_DATE - INTERVAL \u00271 days\u0027 AND h.query_type NOT IN (\u0027UTILITY\u0027) ) SELECT query_id, start_time, database_name, query_type, status, duration_ms, returned_rows, username, full_query_text FROM complete_queries ORDER BY start_time DESC LIMIT 10;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225457
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:55.757399"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225451
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:50.355407"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  5341
                        },
                        {
                            "longValue":  17
                        },
                        {
                            "longValue":  2679
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT query_id, start_time, database_name, query_type, status, DATEDIFF(millisecond, start_time, end_time) as duration_ms, query_text FROM sys_query_history WHERE start_time \u003e= CURRENT_DATE - INTERVAL \u00277 days\u0027 ORDER BY start_time DESC LIMIT 20;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225445
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:29.47594"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225442
                        },
                        {
                            "stringValue":  "2025-07-29 10:51:29.36297"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "OTHER"
                        },
                        {
                            "stringValue":  "failed    "
                        },
                        {
                            "longValue":  4
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  "column \"duration\" does not exist in sys_query_history"
                        },
                        {
                            "stringValue":  "SELECT query_id, start_time, database_name, query_type, status, duration, query_text FROM sys_query_history WHERE start_time \u003e= CURRENT_DATE - INTERVAL \u00277 days\u0027 ORDER BY start_time DESC LIMIT 20;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225403
                        },
                        {
                            "stringValue":  "2025-07-29 10:49:58.255964"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225400
                        },
                        {
                            "stringValue":  "2025-07-29 10:49:53.771537"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  4373
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT listagg(text, \u0027\u0027) within group (order by sequence) as complete_query FROM sys_child_query_text WHERE query_id = 1224653 GROUP BY query_id, child_query_sequence;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225381
                        },
                        {
                            "stringValue":  "2025-07-29 10:49:20.172186"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  1
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ],
                    [
                        {
                            "longValue":  1225378
                        },
                        {
                            "stringValue":  "2025-07-29 10:49:15.00787"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  5067
                        },
                        {
                            "longValue":  8
                        },
                        {
                            "longValue":  776
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT query_id, start_time, LEFT(query_text, 100) as query_preview FROM sys_query_history ORDER BY start_time DESC LIMIT 10;"
                        }
                    ],
                    [
                        {
                            "longValue":  1225372
                        },
                        {
                            "stringValue":  "2025-07-29 10:48:56.938337"
                        },
                        {
                            "stringValue":  "dev"
                        },
                        {
                            "stringValue":  "SELECT"
                        },
                        {
                            "stringValue":  "success   "
                        },
                        {
                            "longValue":  2
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "longValue":  0
                        },
                        {
                            "stringValue":  "IAM:dev_azure_devops_user                                                                                                       "
                        },
                        {
                            "stringValue":  ""
                        },
                        {
                            "stringValue":  "SELECT pg_last_query_id()"
                        }
                    ]
                ],
    "ColumnMetadata":  [
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "query_id",
                               "length":  0,
                               "name":  "query_id",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "start_time",
                               "length":  0,
                               "name":  "start_time",
                               "nullable":  1,
                               "precision":  29,
                               "scale":  6,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "timestamp"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "database_name",
                               "length":  0,
                               "name":  "database_name",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "query_type",
                               "length":  0,
                               "name":  "query_type",
                               "nullable":  1,
                               "precision":  32,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "status",
                               "length":  0,
                               "name":  "status",
                               "nullable":  1,
                               "precision":  10,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "duration_ms",
                               "length":  0,
                               "name":  "duration_ms",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_rows",
                               "length":  0,
                               "name":  "returned_rows",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  false,
                               "isCurrency":  false,
                               "isSigned":  true,
                               "label":  "returned_bytes",
                               "length":  0,
                               "name":  "returned_bytes",
                               "nullable":  1,
                               "precision":  19,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "int8"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "username",
                               "length":  0,
                               "name":  "username",
                               "nullable":  1,
                               "precision":  128,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "bpchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "result",
                               "length":  0,
                               "name":  "result",
                               "nullable":  1,
                               "precision":  512,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           },
                           {
                               "isCaseSensitive":  true,
                               "isCurrency":  false,
                               "isSigned":  false,
                               "label":  "full_query_text",
                               "length":  0,
                               "name":  "full_query_text",
                               "nullable":  1,
                               "precision":  65535,
                               "scale":  0,
                               "schemaName":  "",
                               "tableName":  "",
                               "typeName":  "varchar"
                           }
                       ],
    "TotalNumRows":  20
}
