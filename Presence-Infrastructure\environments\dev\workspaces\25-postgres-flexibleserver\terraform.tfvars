# DEV Environment Configuration - PUBLIC ACCESS
# Basic Configuration
db_name              = "presence-postgres-dev"
key_vault_name       = "presence-kv-dev"
db_username_secret   = "postgres-admin-username"
db_password_secret   = "postgres-admin-password"
location             = "East US"
rg_name              = "presence-rg-dev"

# Server Configuration
db_version    = "13"
sku_name      = "B_Standard_B1ms"  # Basic tier for dev
storage_mb    = 32768              # 32 GB
storage_tier  = "P4"
zone          = 1

# Database Configuration
databases = ["presence_dev", "postgres"]

# Firewall Rules for Public Access (Update with your actual IP ranges)
allowed_ip_ranges = {
  "office_network" = {
    start_ip = "***********"    # Replace with your office IP range
    end_ip   = "*************"
  }
  "developer_home" = {
    start_ip = "************"   # Replace with developer home IP range
    end_ip   = "**************"
  }
  "ci_cd_pipeline" = {
    start_ip = "***********"    # Replace with CI/CD pipeline IP
    end_ip   = "***********"
  }
}

# PostgreSQL Configurations for Development
postgresql_configurations = {
  "log_statement"              = "all"
  "log_min_duration_statement" = "1000"
  "log_connections"            = "on"
  "log_disconnections"         = "on"
  "connection_throttling"      = "on"
}

# Tags
tags = {
  Environment = "dev"
  Project     = "presence-cloud"
  ManagedBy   = "terraform"
  AccessType  = "public"
}
