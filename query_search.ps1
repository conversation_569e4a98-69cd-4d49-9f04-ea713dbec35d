#!/usr/bin/env powershell

# QUERY SEARCH TOOL - Search through logged queries
# Usage: .\query_search.ps1 -SearchTerm "SELECT" -Days 7

param(
    [string]$SearchTerm = "",
    [int]$Days = 7,
    [string]$LogDirectory = "query_logs",
    [string]$QueryId = ""
)

if (!$SearchTerm -and !$QueryId) {
    Write-Host "Usage Examples:" -ForegroundColor Yellow
    Write-Host "  .\query_search.ps1 -SearchTerm 'SELECT * FROM users'" -ForegroundColor Gray
    Write-Host "  .\query_search.ps1 -QueryId '1225488'" -ForegroundColor Gray
    Write-Host "  .\query_search.ps1 -SearchTerm 'CREATE TABLE' -Days 30" -ForegroundColor Gray
    exit
}

if (!(Test-Path $LogDirectory)) {
    Write-Host "Log directory not found: $LogDirectory" -ForegroundColor Red
    Write-Host "Make sure the query logger has been running." -ForegroundColor Yellow
    exit
}

Write-Host "Searching logged queries..." -ForegroundColor Green

# Search in SQL files
$sqlLogDir = "$LogDirectory\sql_files"
$results = @()

if (Test-Path $sqlLogDir) {
    $sqlFiles = Get-ChildItem -Path $sqlLogDir -Filter "*.sql" | Where-Object { 
        $_.LastWriteTime -gt (Get-Date).AddDays(-$Days) 
    }
    
    foreach ($file in $sqlFiles) {
        $content = Get-Content $file.FullName -Raw
        
        $match = $false
        if ($QueryId) {
            if ($file.Name -like "*query_${QueryId}_*") {
                $match = $true
            }
        } elseif ($SearchTerm) {
            if ($content -like "*$SearchTerm*") {
                $match = $true
            }
        }
        
        if ($match) {
            # Extract query ID from filename
            if ($file.Name -match 'query_(\d+)_') {
                $foundQueryId = $matches[1]
                
                # Extract metadata from file content
                $lines = $content -split "`n"
                $startTime = ($lines | Where-Object { $_ -like "-- Start Time:*" }) -replace "-- Start Time: ", ""
                $status = ($lines | Where-Object { $_ -like "-- Status:*" }) -replace "-- Status: ", ""
                $user = ($lines | Where-Object { $_ -like "-- User:*" }) -replace "-- User: ", ""
                $queryType = ($lines | Where-Object { $_ -like "-- Query Type:*" }) -replace "-- Query Type: ", ""
                
                # Extract actual query (after the separator line)
                $separatorIndex = -1
                for ($i = 0; $i -lt $lines.Length; $i++) {
                    if ($lines[$i] -like "*====*") {
                        $separatorIndex = $i
                        break
                    }
                }
                
                $actualQuery = ""
                if ($separatorIndex -ge 0 -and $separatorIndex + 2 -lt $lines.Length) {
                    $actualQuery = ($lines[($separatorIndex + 2)..($lines.Length - 1)] -join "`n").Trim()
                }
                
                $results += [PSCustomObject]@{
                    QueryId = $foundQueryId
                    StartTime = $startTime
                    Status = $status
                    User = $user
                    QueryType = $queryType
                    FileName = $file.Name
                    FilePath = $file.FullName
                    Query = $actualQuery
                }
            }
        }
    }
}

if ($results.Count -eq 0) {
    Write-Host "No matching queries found." -ForegroundColor Yellow
    if ($QueryId) {
        Write-Host "Query ID '$QueryId' not found in logs." -ForegroundColor Red
    } else {
        Write-Host "Search term '$SearchTerm' not found in any queries." -ForegroundColor Red
    }
    exit
}

Write-Host "`nFound $($results.Count) matching queries:" -ForegroundColor Green
Write-Host "=" * 80 -ForegroundColor Magenta

foreach ($result in $results | Sort-Object StartTime -Descending) {
    Write-Host "`nQuery ID: $($result.QueryId)" -ForegroundColor Cyan
    Write-Host "Start Time: $($result.StartTime)" -ForegroundColor Gray
    Write-Host "Status: $($result.Status)" -ForegroundColor $(if ($result.Status -like "*success*") { "Green" } else { "Red" })
    Write-Host "User: $($result.User)" -ForegroundColor Gray
    Write-Host "Type: $($result.QueryType)" -ForegroundColor Gray
    Write-Host "File: $($result.FileName)" -ForegroundColor Gray
    
    if ($QueryId) {
        # Show full query for specific query ID search
        Write-Host "`nCOMPLETE QUERY:" -ForegroundColor Yellow
        Write-Host "-" * 80 -ForegroundColor Yellow
        Write-Host $result.Query -ForegroundColor White
        Write-Host "-" * 80 -ForegroundColor Yellow
    } else {
        # Show preview for text search
        $preview = if ($result.Query.Length -gt 200) { 
            $result.Query.Substring(0, 200) + "..." 
        } else { 
            $result.Query 
        }
        Write-Host "Query Preview: $preview" -ForegroundColor White
    }
    
    Write-Host "-" * 80 -ForegroundColor Gray
}

Write-Host "`nSearch completed!" -ForegroundColor Green

if (!$QueryId -and $results.Count -eq 1) {
    Write-Host "`nTo see the complete query, run:" -ForegroundColor Yellow
    Write-Host ".\query_search.ps1 -QueryId '$($results[0].QueryId)'" -ForegroundColor Cyan
}
