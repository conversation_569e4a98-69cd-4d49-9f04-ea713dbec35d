# Basic Configuration Variables
variable "db_name" {
  type        = string
  description = "Name of the PostgreSQL Flexible Database Server"
}

variable "key_vault_name" {
  type        = string
  description = "Key Vault Name"
}

variable "db_username_secret" {
  type        = string
  description = "Database username secret name in Key Vault"
}

variable "db_password_secret" {
  type        = string
  description = "Database password secret name in Key Vault"
}

variable "location" {
  type        = string
  description = "Azure region where resources will be deployed"
}

variable "rg_name" {
  type        = string
  description = "Resource Group Name"
}

variable "db_version" {
  type        = string
  description = "PostgreSQL version"
  default     = "13"
}

# Server Configuration Variables
variable "sku_name" {
  type        = string
  description = "SKU name for the PostgreSQL server"
  default     = "B_Standard_B1ms"
}

variable "storage_mb" {
  type        = number
  description = "Storage size in MB"
  default     = 32768
}

variable "storage_tier" {
  type        = string
  description = "Storage tier"
  default     = "P4"
}

variable "zone" {
  type        = number
  description = "Availability zone for the server"
  default     = 1
}

# Firewall Rules for Public Access (DEV Environment)
variable "allowed_ip_ranges" {
  type = map(object({
    start_ip = string
    end_ip   = string
  }))
  description = "Map of allowed IP ranges for public access"
  default = {
    "office_network" = {
      start_ip = "***********"
      end_ip   = "*************"
    }
    "developer_home" = {
      start_ip = "************"
      end_ip   = "**************"
    }
  }
}

# Database Configuration
variable "databases" {
  type        = list(string)
  description = "List of databases to create"
  default     = ["presence_dev", "postgres"]
}

variable "postgresql_configurations" {
  type        = map(string)
  description = "PostgreSQL server configurations"
  default = {
    "log_statement"                 = "all"
    "log_min_duration_statement"    = "1000"
    "log_connections"               = "on"
    "log_disconnections"            = "on"
    "connection_throttling"         = "on"
  }
}

# Tags
variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default = {
    Environment = "dev"
    Project     = "presence-cloud"
    ManagedBy   = "terraform"
  }
}
