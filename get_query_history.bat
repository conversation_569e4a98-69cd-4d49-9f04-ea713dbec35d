@echo off
REM Simple batch script to get complete query history from Redshift Serverless
REM Usage: get_query_history.bat [days] [limit]
REM Example: get_query_history.bat 7 20

set DAYS=%1
set LIMIT=%2

if "%DAYS%"=="" set DAYS=1
if "%LIMIT%"=="" set LIMIT=10

echo Getting complete query history for last %DAYS% days (limit: %LIMIT% queries)...

powershell.exe -ExecutionPolicy Bypass -File "get_complete_query_history.ps1" -Days %DAYS% -Limit %LIMIT%

pause
