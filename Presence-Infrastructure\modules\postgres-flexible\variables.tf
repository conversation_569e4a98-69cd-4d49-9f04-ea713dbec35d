# Basic Configuration Variables
variable "db_name" {
  type        = string
  description = "Name of the PostgreSQL Flexible Database Server"
}

variable "key_vault_name" {
  type        = string
  description = "Key Vault Name"
}

variable "db_username_secret" {
  type        = string
  description = "Database username secret name in Key Vault"
}

variable "db_password_secret" {
  type        = string
  description = "Database password secret name in Key Vault"
}

variable "location" {
  type        = string
  description = "Azure region where resources will be deployed"
}

variable "rg_name" {
  type        = string
  description = "Resource Group Name"
}

variable "db_version" {
  type        = string
  description = "PostgreSQL version"
  default     = "13"
}

# Network Configuration Variables
variable "private_access_enabled" {
  type        = bool
  description = "Enable private access (VNet integration)"
  default     = false
}

variable "public_network_access_enabled" {
  type        = bool
  description = "Enable public network access"
  default     = true
}

variable "subnet_id" {
  type        = string
  description = "Subnet ID for private access (required when private_access_enabled is true)"
  default     = null
}

variable "vnet_id" {
  type        = string
  description = "Virtual Network ID for private DNS zone linking"
  default     = null
}

variable "private_dns_zone_name" {
  type        = string
  description = "Name of the private DNS zone for PostgreSQL"
  default     = null
}

variable "dns_zone_link_name" {
  type        = string
  description = "Name of the private DNS zone virtual network link"
  default     = null
}

# Server Configuration Variables
variable "sku_name" {
  type        = string
  description = "SKU name for the PostgreSQL server"
  default     = "B_Standard_B1ms"
}

variable "storage_mb" {
  type        = number
  description = "Storage size in MB"
  default     = 32768
}

variable "storage_tier" {
  type        = string
  description = "Storage tier (P4, P6, P10, P15, P20, P30, P40, P50)"
  default     = "P4"
}

variable "zone" {
  type        = number
  description = "Availability zone for the server"
  default     = 1
}

# High Availability Configuration
variable "high_availability_enabled" {
  type        = bool
  description = "Enable high availability for the PostgreSQL server"
  default     = false
}

variable "standby_availability_zone" {
  type        = number
  description = "Availability zone for the standby server (for HA)"
  default     = 2
}

# Backup Configuration
variable "backup_retention_days" {
  type        = number
  description = "Backup retention period in days"
  default     = 7
}

variable "geo_redundant_backup_enabled" {
  type        = bool
  description = "Enable geo-redundant backup"
  default     = false
}

# Maintenance Window Configuration
variable "maintenance_window" {
  type = object({
    day_of_week  = number
    start_hour   = number
    start_minute = number
  })
  description = "Maintenance window configuration"
  default     = null
}

# Firewall Rules for Public Access
variable "allow_azure_services" {
  type        = bool
  description = "Allow Azure services to access the server"
  default     = true
}

variable "allowed_ip_ranges" {
  type = map(object({
    start_ip = string
    end_ip   = string
  }))
  description = "Map of allowed IP ranges for public access"
  default     = {}
}

# Database Configuration
variable "databases" {
  type        = list(string)
  description = "List of databases to create"
  default     = ["postgres"]
}

variable "postgresql_configurations" {
  type        = map(string)
  description = "PostgreSQL server configurations"
  default     = {}
}

# Tags
variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default     = {}
}