﻿[2025-07-29 16:44:57] [INFO] === AUTOMATIC QUERY LOGGER STARTED ===
[2025-07-29 16:44:57] [INFO] Workgroup: redshift-poc
[2025-07-29 16:44:57] [INFO] Database: dev
[2025-07-29 16:44:57] [INFO] Region: us-east-1
[2025-07-29 16:44:57] [INFO] Check Interval: 1 minutes
[2025-07-29 16:44:57] [INFO] Log Directory: query_logs
[2025-07-29 16:44:57] [INFO] Press Ctrl+C to stop
[2025-07-29 16:44:57] [INFO] Checking for new queries since ID: 0
[2025-07-29 16:45:08] [INFO] Found 18 new queries to log

========================================
QUERY ID: 1225359
========================================
Start Time: 2025-07-29 10:48:35.658994
End Time: 2025-07-29 10:48:40.514364
Database: dev
Query Type: SELECT
Status: success
Duration: 4856 ms
Elapsed Time: 4855370 ms
Queue Time: N/A ms
Execution Time: 678581 ms
Compile Time: 4570191 ms
Planning Time: 76567 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_text FROM sys_query_history WHERE query_id = 1224653;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225359 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225368
========================================
Start Time: 2025-07-29 10:48:51.784367
End Time: 2025-07-29 10:48:56.829756
Database: dev
Query Type: SELECT
Status: success
Duration: 5045 ms
Elapsed Time: 5045389 ms
Queue Time: N/A ms
Execution Time: 681691 ms
Compile Time: 4288776 ms
Planning Time: 77307 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, start_time, query_text FROM sys_query_history WHERE query_id BETWEEN 1224650 AND 1224660 ORDER BY query_id;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225368 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225378
========================================
Start Time: 2025-07-29 10:49:15.00787
End Time: 2025-07-29 10:49:20.074114
Database: dev
Query Type: SELECT
Status: success
Duration: 5067 ms
Elapsed Time: 5066244 ms
Queue Time: N/A ms
Execution Time: 507193 ms
Compile Time: 4773242 ms
Planning Time: 75489 ms
Returned Rows: 8
Returned Bytes: 776
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, start_time, LEFT(query_text, 100) as query_preview FROM sys_query_history ORDER BY start_time DESC LIMIT 10;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225378 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225400
========================================
Start Time: 2025-07-29 10:49:53.771537
End Time: 2025-07-29 10:49:58.144987
Database: dev
Query Type: SELECT
Status: success
Duration: 4373 ms
Elapsed Time: 4373450 ms
Queue Time: N/A ms
Execution Time: 1407544 ms
Compile Time: 3224133 ms
Planning Time: 24961 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT listagg(text, '') within group (order by sequence) as complete_query FROM sys_child_query_text WHERE query_id = 1224653 GROUP BY query_id, child_query_sequence;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225400 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225442
========================================
Start Time: 2025-07-29 10:51:29.36297
End Time: 2025-07-29 10:51:29.366583
Database: dev
Query Type: OTHER
Status: failed
Duration: 4 ms
Elapsed Time: 3613 ms
Queue Time: N/A ms
Execution Time: N/A ms
Compile Time: N/A ms
Planning Time: N/A ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: column "duration" does not exist in sys_query_history

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, start_time, database_name, query_type, status, duration, query_text FROM sys_query_history WHERE start_time >= CURRENT_DATE - INTERVAL '7 days' ORDER BY start_time DESC LIMIT 20;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225442 | Type: OTHER | Status: failed | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225451
========================================
Start Time: 2025-07-29 10:51:50.355407
End Time: 2025-07-29 10:51:55.696909
Database: dev
Query Type: SELECT
Status: success
Duration: 5341 ms
Elapsed Time: 5341502 ms
Queue Time: N/A ms
Execution Time: 639967 ms
Compile Time: 5030388 ms
Planning Time: 80378 ms
Returned Rows: 17
Returned Bytes: 2679
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, start_time, database_name, query_type, status, DATEDIFF(millisecond, start_time, end_time) as duration_ms, query_text FROM sys_query_history WHERE start_time >= CURRENT_DATE - INTERVAL '7 days' ORDER BY start_time DESC LIMIT 20;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225451 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225488
========================================
Start Time: 2025-07-29 10:53:14.162058
End Time: 2025-07-29 10:53:23.551141
Database: dev
Query Type: SELECT
Status: success
Duration: 9389 ms
Elapsed Time: 9389083 ms
Queue Time: N/A ms
Execution Time: 1832302 ms
Compile Time: 7942913 ms
Planning Time: 119622 ms
Returned Rows: 10
Returned Bytes: 3634
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_queries AS ( SELECT h.query_id, h.start_time, h.end_time, h.database_name, h.query_type, h.status, h.username, DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms, h.returned_rows, COALESCE(ct.complete_query_text, h.query_text) as full_query_text FROM sys_query_history h LEFT JOIN ( SELECT query_id, child_query_sequence, listagg(text, '') within group (order by sequence) as complete_query_text FROM sys_child_query_text GROUP BY query_id, child_query_sequence ) ct ON h.query_id = ct.query_id WHERE h.start_time >= CURRENT_DATE - INTERVAL '1 days' AND h.query_type NOT IN ('UTILITY') ) SELECT query_id, start_time, database_name, query_type, status, duration_ms, returned_rows, username, full_query_text FROM complete_queries ORDER BY start_time DESC LIMIT 10;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225488 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225551
========================================
Start Time: 2025-07-29 10:55:01.007067
End Time: 2025-07-29 10:55:08.633584
Database: dev
Query Type: SELECT
Status: success
Duration: 7626 ms
Elapsed Time: 7626517 ms
Queue Time: N/A ms
Execution Time: 1708862 ms
Compile Time: 6104950 ms
Planning Time: 126817 ms
Returned Rows: 5
Returned Bytes: 3564
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_queries AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.start_time >= CURRENT_DATE - INTERVAL '1 days'\n        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries like SET statements\n        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries\n)\nSELECT \n    query_id,\n    start_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    returned_rows,\n    returned_bytes,\n    username,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_queries\nORDER BY start_time DESC\nLIMIT 5;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225551 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225674
========================================
Start Time: 2025-07-29 10:59:36.419505
End Time: 2025-07-29 10:59:42.766953
Database: dev
Query Type: SELECT
Status: success
Duration: 6347 ms
Elapsed Time: 6347448 ms
Queue Time: N/A ms
Execution Time: 1828033 ms
Compile Time: 4720976 ms
Planning Time: 135517 ms
Returned Rows: 1
Returned Bytes: 1106
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_query AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        h.elapsed_time,\n        h.queue_time,\n        h.execution_time,\n        h.compile_time,\n        h.planning_time,\n        h.lock_wait_time,\n        h.usage_limit,\n        h.compute_type,\n        h.redshift_version,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.query_id = 1225488\n)\nSELECT \n    query_id,\n    start_time,\n    end_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    elapsed_time,\n    queue_time,\n    execution_time,\n    compile_time,\n    planning_time,\n    lock_wait_time,\n    returned_rows,\n    returned_bytes,\n    username,\n    usage_limit,\n    compute_type,\n    redshift_version,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_query;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225674 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225686
========================================
Start Time: 2025-07-29 10:59:58.310477
End Time: 2025-07-29 10:59:59.810813
Database: dev
Query Type: SELECT
Status: success
Duration: 1500 ms
Elapsed Time: 1500336 ms
Queue Time: N/A ms
Execution Time: 1245535 ms
Compile Time: 72813 ms
Planning Time: 134213 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_query AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        h.elapsed_time,\n        h.queue_time,\n        h.execution_time,\n        h.compile_time,\n        h.planning_time,\n        h.lock_wait_time,\n        h.usage_limit,\n        h.compute_type,\n        h.redshift_version,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.query_id = 1224653\n)\nSELECT \n    query_id,\n    start_time,\n    end_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    elapsed_time,\n    queue_time,\n    execution_time,\n    compile_time,\n    planning_time,\n    lock_wait_time,\n    returned_rows,\n    returned_bytes,\n    username,\n    usage_limit,\n    compute_type,\n    redshift_version,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_query;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225686 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225773
========================================
Start Time: 2025-07-29 11:01:24.952644
End Time: 2025-07-29 11:01:26.884146
Database: dev
Query Type: SELECT
Status: success
Duration: 1932 ms
Elapsed Time: 1931502 ms
Queue Time: N/A ms
Execution Time: 1748534 ms
Compile Time: 793 ms
Planning Time: 134060 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_query AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        h.elapsed_time,\n        h.queue_time,\n        h.execution_time,\n        h.compile_time,\n        h.planning_time,\n        h.lock_wait_time,\n        h.usage_limit,\n        h.compute_type,\n        h.redshift_version,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.query_id = 1073946732\n)\nSELECT \n    query_id,\n    start_time,\n    end_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    elapsed_time,\n    queue_time,\n    execution_time,\n    compile_time,\n    planning_time,\n    lock_wait_time,\n    returned_rows,\n    returned_bytes,\n    username,\n    usage_limit,\n    compute_type,\n    redshift_version,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_query;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225773 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225784
========================================
Start Time: 2025-07-29 11:01:39.196054
End Time: 2025-07-29 11:01:40.991887
Database: dev
Query Type: SELECT
Status: success
Duration: 1795 ms
Elapsed Time: 1795833 ms
Queue Time: N/A ms
Execution Time: 1537076 ms
Compile Time: 84393 ms
Planning Time: 126335 ms
Returned Rows: 20
Returned Bytes: 13854
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_queries AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.start_time >= CURRENT_DATE - INTERVAL '1 days'\n        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries like SET statements\n        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries\n)\nSELECT \n    query_id,\n    start_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    returned_rows,\n    returned_bytes,\n    username,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_queries\nORDER BY start_time DESC\nLIMIT 20;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225784 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225798
========================================
Start Time: 2025-07-29 11:02:11.441711
End Time: 2025-07-29 11:02:35.816149
Database: dev
Query Type: SELECT
Status: success
Duration: 24375 ms
Elapsed Time: 24374438 ms
Queue Time: N/A ms
Execution Time: 616436 ms
Compile Time: 24045376 ms
Planning Time: 83186 ms
Returned Rows: 10
Returned Bytes: 13107
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, start_time, username, LEFT(query_text, 200) as query_preview, query_text FROM sys_query_history WHERE start_time >= CURRENT_DATE - INTERVAL '1 days' AND query_type NOT IN ('UTILITY') AND query_text NOT LIKE 'SET application_name%' AND query_text NOT LIKE 'SELECT pg_last_query_id%' ORDER BY start_time DESC LIMIT 10;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225798 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225829
========================================
Start Time: 2025-07-29 11:02:44.811703
End Time: 2025-07-29 11:02:46.576694
Database: dev
Query Type: SELECT
Status: success
Duration: 1765 ms
Elapsed Time: 1764991 ms
Queue Time: N/A ms
Execution Time: 1577913 ms
Compile Time: 804 ms
Planning Time: 136481 ms
Returned Rows: 1
Returned Bytes: 563
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_query AS (\n    SELECT \n        h.query_id,\n        h.start_time,\n        h.end_time,\n        h.database_name,\n        h.query_type,\n        h.status,\n        h.username,\n        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,\n        h.returned_rows,\n        h.returned_bytes,\n        h.error_message,\n        h.elapsed_time,\n        h.queue_time,\n        h.execution_time,\n        h.compile_time,\n        h.planning_time,\n        h.lock_wait_time,\n        h.usage_limit,\n        h.compute_type,\n        h.redshift_version,\n        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version\n        COALESCE(ct.complete_query_text, h.query_text) as full_query_text\n    FROM sys_query_history h\n    LEFT JOIN (\n        SELECT \n            query_id,\n            child_query_sequence,\n            listagg(text, '') within group (order by sequence) as complete_query_text\n        FROM sys_child_query_text\n        GROUP BY query_id, child_query_sequence\n    ) ct ON h.query_id = ct.query_id\n    WHERE h.query_id = 1225451\n)\nSELECT \n    query_id,\n    start_time,\n    end_time,\n    database_name,\n    query_type,\n    status,\n    duration_ms,\n    elapsed_time,\n    queue_time,\n    execution_time,\n    compile_time,\n    planning_time,\n    lock_wait_time,\n    returned_rows,\n    returned_bytes,\n    username,\n    usage_limit,\n    compute_type,\n    redshift_version,\n    CASE \n        WHEN error_message IS NOT NULL THEN error_message\n        ELSE 'Success'\n    END as result,\n    full_query_text\nFROM complete_query;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225829 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225901
========================================
Start Time: 2025-07-29 11:05:04.369897
End Time: 2025-07-29 11:05:12.501649
Database: dev
Query Type: SELECT
Status: success
Duration: 8132 ms
Elapsed Time: 8131752 ms
Queue Time: N/A ms
Execution Time: 1134453 ms
Compile Time: 7050267 ms
Planning Time: 24597 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query_id, listagg(text, '') within group (order by sequence) as complete_query FROM sys_child_query_text WHERE query_id = 1224653 GROUP BY query_id;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225901 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1225918
========================================
Start Time: 2025-07-29 11:05:28.4948
End Time: 2025-07-29 11:05:28.501978
Database: dev
Query Type: SELECT
Status: failed
Duration: 7 ms
Elapsed Time: 7178 ms
Queue Time: N/A ms
Execution Time: N/A ms
Compile Time: N/A ms
Planning Time: N/A ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: permission denied for relation stl_query

COMPLETE QUERY TEXT:
----------------------------------------
SELECT query, starttime, endtime, aborted, userid FROM stl_query WHERE query = 1224653;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1225918 | Type: SELECT | Status: failed | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1226134
========================================
Start Time: 2025-07-29 11:14:43.861285
End Time: 2025-07-29 11:14:43.863547
Database: dev
Query Type: SELECT
Status: success
Duration: 2 ms
Elapsed Time: 2262 ms
Queue Time: N/A ms
Execution Time: N/A ms
Compile Time: N/A ms
Planning Time: 1912 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
SELECT 'Query Logger Test' as test_message, CURRENT_TIMESTAMP as test_time;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1226134 | Type: SELECT | Status: success | User: IAM:dev_azure_devops_user

========================================
QUERY ID: 1226142
========================================
Start Time: 2025-07-29 11:15:02.144275
End Time: N/A
Database: dev
Query Type: SELECT
Status: running
Duration: N/A ms
Elapsed Time: 6107099 ms
Queue Time: N/A ms
Execution Time: N/A ms
Compile Time: 5728887 ms
Planning Time: 139573 ms
Returned Rows: 0
Returned Bytes: 0
User: IAM:dev_azure_devops_user
Result: 

COMPLETE QUERY TEXT:
----------------------------------------
WITH complete_queries AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        h.elapsed_time,
        h.queue_time,
        h.execution_time,
        h.compile_time,
        h.planning_time,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.query_id > 0
        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries
        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries
        AND h.query_text NOT LIKE 'SELECT pg_last_query_id%'  -- Exclude internal queries
        AND h.start_time >= CURRENT_DATE - INTERVAL '7 days'  -- Only recent queries
)
SELECT 
    query_id,
    start_time,
    end_time,
    database_name,
    query_type,
    status,
    duration_ms,
    elapsed_time,
    queue_time,
    execution_time,
    compile_time,
    planning_time,
    returned_rows,
    returned_bytes,
    username,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_queries
ORDER BY query_id ASC;
----------------------------------------

[2025-07-29 16:45:11] [INFO] Logged Query ID: 1226142 | Type: SELECT | Status: running | User: IAM:dev_azure_devops_user
[2025-07-29 16:45:11] [INFO] Successfully logged 18 new queries. Latest ID: 1226142
[2025-07-29 16:45:11] [INFO] Waiting 1 minutes before next check...
