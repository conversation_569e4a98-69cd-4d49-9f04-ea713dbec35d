#!/usr/bin/env powershell

# Script to backup recent queries before they age out of retention period
# This will help prevent losing query text in the future
# Usage: .\backup_recent_queries.ps1

param(
    [int]$Days = 1,
    [string]$WorkgroupName = "redshift-poc",
    [string]$Database = "dev",
    [string]$Region = "us-east-1"
)

Write-Host "Backing up all queries from last $Days days..." -ForegroundColor Green

# SQL query to get all queries with complete text
$sql = @"
WITH complete_queries AS (
    SELECT 
        h.query_id,
        h.start_time,
        h.end_time,
        h.database_name,
        h.query_type,
        h.status,
        h.username,
        DATEDIFF(millisecond, h.start_time, h.end_time) as duration_ms,
        h.returned_rows,
        h.returned_bytes,
        h.error_message,
        h.elapsed_time,
        h.queue_time,
        h.execution_time,
        h.compile_time,
        h.planning_time,
        h.lock_wait_time,
        -- Use complete query text from sys_child_query_text if available, otherwise use truncated version
        COALESCE(ct.complete_query_text, h.query_text) as full_query_text
    FROM sys_query_history h
    LEFT JOIN (
        SELECT 
            query_id,
            child_query_sequence,
            listagg(text, '') within group (order by sequence) as complete_query_text
        FROM sys_child_query_text
        GROUP BY query_id, child_query_sequence
    ) ct ON h.query_id = ct.query_id
    WHERE h.start_time >= CURRENT_DATE - INTERVAL '$Days days'
        AND h.query_type NOT IN ('UTILITY')  -- Exclude utility queries
        AND h.query_text NOT LIKE 'SET application_name%'  -- Exclude Data API internal queries
        AND h.query_text NOT LIKE 'SELECT pg_last_query_id%'  -- Exclude internal queries
)
SELECT 
    query_id,
    start_time,
    end_time,
    database_name,
    query_type,
    status,
    duration_ms,
    elapsed_time,
    queue_time,
    execution_time,
    compile_time,
    planning_time,
    lock_wait_time,
    returned_rows,
    returned_bytes,
    username,
    CASE 
        WHEN error_message IS NOT NULL THEN error_message
        ELSE 'Success'
    END as result,
    full_query_text
FROM complete_queries
ORDER BY start_time DESC;
"@

try {
    Write-Host "Executing backup query on Redshift Serverless..." -ForegroundColor Yellow
    
    # Execute the query
    $result = aws redshift-data execute-statement `
        --workgroup-name $WorkgroupName `
        --database $Database `
        --sql $sql `
        --region $Region | ConvertFrom-Json
    
    $statementId = $result.Id
    Write-Host "Query submitted with ID: $statementId" -ForegroundColor Cyan
    
    # Wait for query to complete
    do {
        Start-Sleep -Seconds 2
        $status = aws redshift-data describe-statement --id $statementId --region $Region | ConvertFrom-Json
        Write-Host "Query status: $($status.Status)" -ForegroundColor Yellow
    } while ($status.Status -eq "RUNNING" -or $status.Status -eq "SUBMITTED" -or $status.Status -eq "PICKED" -or $status.Status -eq "STARTED")
    
    if ($status.Status -eq "FINISHED") {
        Write-Host "Query completed successfully!" -ForegroundColor Green
        Write-Host "Found $($status.ResultRows) queries to backup" -ForegroundColor Green
        
        if ($status.ResultRows -eq 0) {
            Write-Host "No queries found to backup." -ForegroundColor Yellow
            return
        }
        
        Write-Host "Fetching results..." -ForegroundColor Yellow
        
        # Get the results
        $queryResults = aws redshift-data get-statement-result --id $statementId --region $Region | ConvertFrom-Json
        
        # Create backup directory
        $backupDir = "query_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        
        Write-Host "`nBacking up $($queryResults.TotalNumRows) queries to directory: $backupDir" -ForegroundColor Magenta
        
        # Save complete backup as JSON
        $completeBackupFile = "$backupDir\complete_backup.json"
        $queryResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $completeBackupFile -Encoding UTF8
        
        # Create individual SQL files for each query
        $index = 1
        foreach ($record in $queryResults.Records) {
            $queryId = $record[0].longValue
            $startTime = $record[1].stringValue
            $endTime = if ($record[2].stringValue) { $record[2].stringValue } else { "N/A" }
            $database = $record[3].stringValue
            $queryType = $record[4].stringValue
            $status = $record[5].stringValue.Trim()
            $durationMs = if ($record[6].longValue) { $record[6].longValue } else { "N/A" }
            $username = $record[14].stringValue.Trim()
            $result = $record[15].stringValue
            $fullQuery = $record[16].stringValue
            
            # Create individual SQL file
            $sqlFileName = "$backupDir\query_${queryId}_${index}.sql"
            $sqlHeader = @"
-- Query ID: $queryId
-- Start Time: $startTime
-- End Time: $endTime
-- Database: $database
-- Query Type: $queryType
-- Status: $status
-- Duration: $durationMs ms
-- User: $username
-- Result: $result
-- ================================================

$fullQuery
"@
            $sqlHeader | Out-File -FilePath $sqlFileName -Encoding UTF8
            
            Write-Host "  ✓ Query $queryId saved to: $sqlFileName" -ForegroundColor Gray
            $index++
        }
        
        # Create summary report
        $summaryFile = "$backupDir\backup_summary.txt"
        $summary = @"
REDSHIFT QUERY BACKUP SUMMARY
Generated: $(Get-Date)
Workgroup: $WorkgroupName
Database: $Database
Period: Last $Days days
Total Queries: $($queryResults.TotalNumRows)

FILES CREATED:
- complete_backup.json: Complete backup in JSON format
- query_[ID]_[#].sql: Individual SQL files for each query
- backup_summary.txt: This summary file

QUERY BREAKDOWN:
"@
        
        # Add query breakdown to summary
        $index = 1
        foreach ($record in $queryResults.Records) {
            $queryId = $record[0].longValue
            $startTime = $record[1].stringValue
            $status = $record[5].stringValue.Trim()
            $queryType = $record[4].stringValue
            
            $summary += "`n$index. Query ID: $queryId | Time: $startTime | Type: $queryType | Status: $status"
            $index++
        }
        
        $summary | Out-File -FilePath $summaryFile -Encoding UTF8
        
        Write-Host "`n" + "=" * 80 -ForegroundColor Green
        Write-Host "BACKUP COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "=" * 80 -ForegroundColor Green
        Write-Host "Backup Directory: $backupDir" -ForegroundColor Cyan
        Write-Host "Total Queries Backed Up: $($queryResults.TotalNumRows)" -ForegroundColor Cyan
        Write-Host "Complete JSON Backup: $completeBackupFile" -ForegroundColor Cyan
        Write-Host "Summary Report: $summaryFile" -ForegroundColor Cyan
        Write-Host "`nAll your queries are now safely backed up!" -ForegroundColor Green
        
    } else {
        Write-Host "Query failed with status: $($status.Status)" -ForegroundColor Red
        if ($status.Error) {
            Write-Host "Error: $($status.Error)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nBackup script completed!" -ForegroundColor Green
